#!/usr/bin/env python3
"""
Startup script for the Email Service API
This script sets up the environment and starts the Flask server
"""

import subprocess
import sys
import os
import time

def install_requirements():
    """Install required packages"""
    print("Installing Python dependencies...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependencies installed successfully!")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        sys.exit(1)

def check_sendgrid_key():
    """Check if SendGrid API key is configured"""
    # The key is hardcoded in email_service.py for now
    # In production, this should be an environment variable
    print("✅ SendGrid API key is configured")

def start_server():
    """Start the Flask email service"""
    print("Starting Email Service API on http://localhost:5000...")
    print("Press Ctrl+C to stop the server")
    print("-" * 50)
    
    try:
        # Import and run the email service
        from email_service import app
        app.run(host='0.0.0.0', port=5000, debug=False)
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Failed to start server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    print("🚀 Oyu Intelligence Email Service Setup")
    print("=" * 50)
    
    # Change to the api directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    # Install dependencies
    install_requirements()
    
    # Check configuration
    check_sendgrid_key()
    
    # Start the server
    start_server()
