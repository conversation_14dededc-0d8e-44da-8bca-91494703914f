#!/usr/bin/env python3
"""
Email Service API using Flask and SendGrid
Handles contact form submissions from the Next.js frontend
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
import sendgrid
from sendgrid.helpers.mail import Mail
import os
import logging
from datetime import datetime
import re

# Try to load environment variables from .env.local
try:
    from dotenv import load_dotenv
    load_dotenv('.env.local')
    load_dotenv('../.env.local')  # Also try parent directory
except ImportError:
    print("Note: python-dotenv not installed. Using system environment variables.")

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# SendGrid configuration - Load from environment variables
SENDGRID_API_KEY = os.getenv('SENDGRID_API_KEY')
FROM_EMAIL = os.getenv('FROM_EMAIL', '<EMAIL>')  # Verified sender email
TO_EMAIL = os.getenv('TO_EMAIL', '<EMAIL>')       # Where contact form emails are sent

# Test mode - set to False for production, True for development
TEST_MODE = os.getenv('TEST_MODE', 'False').lower() == 'true'

def validate_email(email):
    """Validate email format"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def sanitize_input(text):
    """Basic input sanitization"""
    if not text:
        return ""
    # Remove any potential HTML/script tags
    text = re.sub(r'<[^>]*>', '', str(text))
    # Limit length
    return text[:1000]

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'service': 'email-service'
    }), 200

@app.route('/send-email', methods=['POST'])
def send_email():
    """Send email using SendGrid"""
    try:
        # Get JSON data from request
        data = request.get_json()
        
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        # Extract and validate required fields
        name = sanitize_input(data.get('name', ''))
        email = sanitize_input(data.get('email', ''))
        subject = sanitize_input(data.get('subject', ''))
        message = sanitize_input(data.get('message', ''))
        
        # Validation
        if not all([name, email, subject, message]):
            return jsonify({'error': 'All fields are required'}), 400
        
        if not validate_email(email):
            return jsonify({'error': 'Invalid email format'}), 400
        
        # Create professional email content
        email_subject = f"New Contact Form Submission: {subject}"

        # Create HTML email content
        html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Form Submission</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; text-align: center;">New Contact Form Submission</h1>
    </div>

    <div style="background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #ddd;">
        <p style="font-size: 16px; margin-bottom: 20px;">You have received a new message through your website contact form:</p>

        <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
            <tr>
                <td style="padding: 10px; border-bottom: 1px solid #ddd; font-weight: bold; width: 100px;">Name:</td>
                <td style="padding: 10px; border-bottom: 1px solid #ddd;">{name}</td>
            </tr>
            <tr>
                <td style="padding: 10px; border-bottom: 1px solid #ddd; font-weight: bold;">Email:</td>
                <td style="padding: 10px; border-bottom: 1px solid #ddd;"><a href="mailto:{email}" style="color: #667eea; text-decoration: none;">{email}</a></td>
            </tr>
            <tr>
                <td style="padding: 10px; border-bottom: 1px solid #ddd; font-weight: bold;">Subject:</td>
                <td style="padding: 10px; border-bottom: 1px solid #ddd;">{subject}</td>
            </tr>
        </table>

        <div style="background: white; padding: 20px; border-radius: 5px; border-left: 4px solid #667eea;">
            <h3 style="margin-top: 0; color: #333;">Message:</h3>
            <p style="margin-bottom: 0; white-space: pre-wrap;">{message}</p>
        </div>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; font-size: 14px; color: #666;">
            <p><strong>Received:</strong> {datetime.now().strftime('%B %d, %Y at %I:%M %p')}</p>
            <p><strong>Source:</strong> Oyu Intelligence Website Contact Form</p>
            <p><strong>Website:</strong> <a href="https://oyuintelligence.com" style="color: #667eea;">oyuintelligence.com</a></p>
        </div>
    </div>

    <div style="text-align: center; margin-top: 20px; font-size: 12px; color: #999;">
        <p>This email was sent from your website contact form. To reply, use the email address provided above.</p>
    </div>
</body>
</html>
        """

        # Create plain text version for better deliverability
        plain_content = f"""
New Contact Form Submission

Name: {name}
Email: {email}
Subject: {subject}

Message:
{message}

---
Received: {datetime.now().strftime('%B %d, %Y at %I:%M %p')}
Source: Oyu Intelligence Website Contact Form

To reply to this message, email: {email}
        """

        # Create SendGrid mail object with improved formatting
        mail = Mail(
            from_email=FROM_EMAIL,
            to_emails=TO_EMAIL,
            subject=email_subject,
            html_content=html_content
        )
        
        # Send email
        if TEST_MODE:
            # Simulate email sending for testing
            logger.info("TEST MODE: Email would be sent with the following content:")
            logger.info(f"From: {FROM_EMAIL}")
            logger.info(f"To: {TO_EMAIL}")
            logger.info(f"Subject: {email_subject}")
            logger.info(f"Content: {plain_content[:100]}...")

            return jsonify({
                'success': True,
                'message': 'Email sent successfully (TEST MODE)',
                'status_code': 202
            }), 200
        else:
            # Actually send email via SendGrid
            try:
                sg = sendgrid.SendGridAPIClient(api_key=SENDGRID_API_KEY)
                response = sg.send(mail)

                logger.info(f"Email sent successfully. Status code: {response.status_code}")
                if response.body:
                    logger.info(f"Response body: {response.body}")

                return jsonify({
                    'success': True,
                    'message': 'Email sent successfully',
                    'status_code': response.status_code
                }), 200

            except Exception as sendgrid_error:
                logger.error(f"SendGrid error: {str(sendgrid_error)}")
                if hasattr(sendgrid_error, 'status_code'):
                    logger.error(f"SendGrid status code: {sendgrid_error.status_code}")
                if hasattr(sendgrid_error, 'body'):
                    logger.error(f"SendGrid error body: {sendgrid_error.body}")
                raise sendgrid_error
        
    except Exception as e:
        logger.error(f"Error sending email: {str(e)}")
        logger.error(f"Error type: {type(e).__name__}")

        # Check if it's a SendGrid specific error
        if hasattr(e, 'status_code'):
            logger.error(f"SendGrid status code: {e.status_code}")
        if hasattr(e, 'body'):
            logger.error(f"SendGrid error body: {e.body}")

        return jsonify({
            'error': 'Failed to send email',
            'details': str(e)
        }), 500

@app.route('/send-email', methods=['OPTIONS'])
def handle_preflight():
    """Handle CORS preflight requests"""
    return jsonify({'status': 'ok'}), 200

def validate_environment():
    """Validate required environment variables"""
    required_vars = {
        'SENDGRID_API_KEY': SENDGRID_API_KEY,
        'FROM_EMAIL': FROM_EMAIL,
        'TO_EMAIL': TO_EMAIL
    }

    missing_vars = [var for var, value in required_vars.items() if not value]

    if missing_vars:
        logger.error("Missing required environment variables:")
        for var in missing_vars:
            logger.error(f"  - {var}")
        logger.error("Please check your .env file or environment configuration")
        return False

    return True

if __name__ == '__main__':
    logger.info("Starting Email Service API...")

    # Validate environment variables
    if not validate_environment():
        logger.error("Environment validation failed!")
        exit(1)

    logger.info(f"From Email: {FROM_EMAIL}")
    logger.info(f"To Email: {TO_EMAIL}")
    logger.info(f"Test Mode: {TEST_MODE}")

    # Run the Flask app
    app.run(host='0.0.0.0', port=5000, debug=False)
