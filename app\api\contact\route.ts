import { NextRequest, NextResponse } from 'next/server';

// Contact form data interface
interface ContactFormData {
  name: string;
  email: string;
  subject: string;
  message: string;
}

// Validation function
function validateContactData(data: any): data is ContactFormData {
  return (
    typeof data.name === 'string' &&
    typeof data.email === 'string' &&
    typeof data.subject === 'string' &&
    typeof data.message === 'string' &&
    data.name.trim().length > 0 &&
    data.email.trim().length > 0 &&
    data.subject.trim().length > 0 &&
    data.message.trim().length > 0
  );
}

// Email validation
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export async function POST(request: NextRequest) {
  console.log('🔥 Next.js API: Contact form submission received');

  try {
    const body = await request.json();
    console.log('📝 Next.js API: Request body:', body);

    // Validate request data
    if (!validateContactData(body)) {
      console.log('❌ Next.js API: Validation failed');
      return NextResponse.json(
        { error: 'Invalid or missing required fields' },
        { status: 400 }
      );
    }

    // Additional email validation
    if (!isValidEmail(body.email)) {
      console.log('❌ Next.js API: Email validation failed');
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    console.log('✅ Next.js API: Validation passed');

    // Prepare data for Python API
    const contactData = {
      name: body.name.trim(),
      email: body.email.trim().toLowerCase(),
      subject: body.subject.trim(),
      message: body.message.trim(),
    };

    // Call Python email service
    const pythonApiUrl = process.env.PYTHON_API_URL ?? 'http://localhost:5000';
    console.log('🔗 Next.js API: Calling Python API at:', pythonApiUrl);

    // Check if Python API is available
    if (!pythonApiUrl || pythonApiUrl === 'http://localhost:5000') {
      // For Vercel deployment without Python API, return a demo response
      console.log('⚠️ Next.js API: Python API not configured, returning demo response');

      return NextResponse.json({
        success: true,
        message: 'Contact form submitted successfully (Demo mode - Python API not configured)',
        demo: true,
        data: {
          note: 'To enable real email sending, deploy the Python API and set PYTHON_API_URL environment variable',
          submitted_data: contactData
        }
      });
    }

    try {
      const response = await fetch(`${pythonApiUrl}/send-email`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(contactData),
      });

      console.log('📡 Next.js API: Python API response status:', response.status);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('❌ Next.js API: Python API error:', errorData);

        return NextResponse.json(
          {
            error: 'Failed to send email',
            details: errorData.error ?? 'Unknown error occurred'
          },
          { status: 500 }
        );
      }

      const result = await response.json();
      console.log('✅ Next.js API: Python API success:', result);

      return NextResponse.json({
        success: true,
        message: 'Email sent successfully',
        data: result
      });

    } catch (fetchError) {
      console.error('❌ Next.js API: Failed to connect to Python API:', fetchError);

      // Fallback for when Python API is not reachable
      return NextResponse.json({
        success: true,
        message: 'Contact form submitted successfully (Python API unavailable)',
        fallback: true,
        data: {
          note: 'Your message was received but email sending is currently unavailable. Please try again later or contact us directly.',
          submitted_data: contactData
        }
      });
    }

  } catch (error) {
    console.error('❌ Next.js API: Contact API error:', error);

    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Handle OPTIONS request for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
