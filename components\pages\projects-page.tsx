"use client";

import { motion } from "framer-motion";
import Link from "next/link";
import { useState } from "react";

import { ProjectCard } from "@/components/sub/project-card";
import { SectionTitle } from "@/components/ui/section-title";
import { ALL_PROJECTS } from "@/constants";
import { slideInFromLeft } from "@/lib/motion";

// Define project categories
const categories = [
  "All",
  "AI Automation",
  "Mobile App",
  "Web Development",
  "Social Media",
];

export const ProjectsPage = () => {
  const [activeCategory, setActiveCategory] = useState("All");

  // Filter projects based on active category
  const filteredProjects = activeCategory === "All"
    ? ALL_PROJECTS
    : ALL_PROJECTS.filter(project => project.category === activeCategory);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen py-20 px-4 mt-16">
      <div className="mb-16">
        <SectionTitle
          subtitle="Our Work"
          title="Explore Our Projects"
          highlightWord="Projects"
          description="Discover our portfolio of successful projects across various industries. Each project showcases our expertise, creativity, and commitment to delivering exceptional results."
          size="large"
          alignment="center"
          icon={
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
          }
        />
      </div>

      {/* Category Filter */}
      <motion.div
        variants={slideInFromLeft(0.5)}
        className="flex flex-wrap justify-center gap-4 mb-12"
      >
        {categories.map((category) => (
          <button
            key={category}
            onClick={() => setActiveCategory(category)}
            className={`px-6 py-2 rounded-full transition-all duration-300 ${
              activeCategory === category
                ? "bg-gradient-to-r from-purple-500 to-cyan-500 text-white"
                : "bg-[rgba(3,0,20,0.5)] border border-[#7042F88B] text-gray-300 hover:border-purple-500"
            }`}
          >
            {category}
          </button>
        ))}
      </motion.div>

      {/* Projects Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl">
        {filteredProjects.map((project, index) => (
          <motion.div
            key={project.slug}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
          >
            <Link href={`/projects/${project.slug}`}>
              <ProjectCard
                src={project.image}
                title={project.title}
                description={project.shortDescription ?? project.description.substring(0, 120) + "..."}
                isLink={false}
              />
            </Link>
          </motion.div>
        ))}
      </div>

      {filteredProjects.length === 0 && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-gray-400 text-center mt-12"
        >
          No projects found in this category. Please check back later.
        </motion.div>
      )}

      {/* Back to Home Button */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.5 }}
        className="mt-16"
      >
        <Link
          href="/"
          className="px-8 py-3 button-primary text-white rounded-lg hover:opacity-90 transition-opacity"
        >
          Back to Home
        </Link>
      </motion.div>
    </div>
  );
};
