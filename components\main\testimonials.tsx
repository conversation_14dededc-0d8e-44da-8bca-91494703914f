"use client";

import { StarIcon } from "@heroicons/react/24/solid";
import { AnimatePresence, motion, useInView } from "framer-motion";
import Image from "next/image";
import { useCallback, useEffect, useRef, useState } from "react";

import { SectionTitle } from "@/components/ui/section-title";
import { TestimonialType } from "@/types";

// Add type for animation options
type KeyframeAnimationOptions = {
  duration: number;
  easing: string;
  fill: 'forwards' | 'backwards' | 'both' | 'none';
};

// Testimonial card component
const TestimonialCard = ({
  testimonial,
  index,
  isActive
}: {
  testimonial: TestimonialType;
  index: number;
  isActive: boolean;
}) => {
  const cardRef = useRef(null);
  const isInView = useInView(cardRef, { once: true });

  return (
    <motion.div
      ref={cardRef}
      initial={{ opacity: 0, y: 50 }}
      animate={isInView ? {
        opacity: isActive ? 1 : 0.5,
        y: 0,
        scale: isActive ? 1 : 0.95
      } : { opacity: 0, y: 50 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      className="relative group h-full"
    >
      {/* Enhanced glassmorphism card with gradient border */}
      <div className={`relative h-full flex flex-col p-4 sm:p-6 lg:p-8 rounded-2xl backdrop-blur-xl transition-all duration-500 overflow-hidden ${
        isActive
          ? "bg-gradient-to-br from-[rgba(30,10,60,0.8)] via-[rgba(15,5,40,0.9)] to-[rgba(3,0,20,0.95)] border-2 border-transparent shadow-2xl shadow-purple-500/20"
          : "bg-gradient-to-br from-[rgba(20,5,40,0.6)] via-[rgba(10,3,25,0.7)] to-[rgba(3,0,20,0.8)] border border-[#7042F88B] shadow-lg shadow-purple-900/10"
      }`}
      >
        {/* Animated gradient border */}
        {isActive && (
          <div className="absolute inset-0 rounded-2xl p-[2px] bg-gradient-to-r from-purple-500 via-cyan-500 to-purple-500 bg-[length:200%_200%] animate-gradient-xy">
            <div className="h-full w-full rounded-2xl bg-gradient-to-br from-[rgba(30,10,60,0.95)] via-[rgba(15,5,40,0.98)] to-[rgba(3,0,20,1)]" />
          </div>
        )}

        {/* Content container */}
        <div className="relative z-10 flex flex-col h-full">
          {/* Enhanced quote icon with glow effect */}
          <motion.div
            className="relative mb-4 sm:mb-6"
            animate={isActive ? {
              scale: [1, 1.05, 1],
              rotate: [0, 2, -2, 0]
            } : {}}
            transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
          >
            <div className="absolute inset-0 bg-gradient-to-r from-purple-500/30 to-cyan-500/30 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
            <div className="relative text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-cyan-400">
              <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg" className="drop-shadow-lg sm:w-10 sm:h-10">
                <path d="M10.667 13.333H5.33366C4.96547 13.333 4.66699 13.0345 4.66699 12.6663V7.33301C4.66699 6.96482 4.96547 6.66634 5.33366 6.66634H10.667C11.0352 6.66634 11.3337 6.96482 11.3337 7.33301V12.6663C11.3337 13.0345 11.0352 13.333 10.667 13.333Z" stroke="url(#quote-gradient)" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M26.6663 13.333H21.333C20.9648 13.333 20.6663 13.0345 20.6663 12.6663V7.33301C20.6663 6.96482 20.9648 6.66634 21.333 6.66634H26.6663C27.0345 6.66634 27.333 6.96482 27.333 7.33301V12.6663C27.333 13.0345 27.0345 13.333 26.6663 13.333Z" stroke="url(#quote-gradient)" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round"/>
                <defs>
                  <linearGradient id="quote-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stopColor="#a855f7" />
                    <stop offset="100%" stopColor="#06b6d4" />
                  </linearGradient>
                </defs>
              </svg>
            </div>
          </motion.div>

          {/* Enhanced testimonial text */}
          <motion.p
            className="text-gray-200 italic mb-6 sm:mb-8 flex-grow text-sm sm:text-base lg:text-lg leading-relaxed font-light"
            animate={isActive ? { opacity: [0.8, 1, 0.8] } : {}}
            transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
          >
            &ldquo;{testimonial.testimonial}&rdquo;
          </motion.p>

          {/* Enhanced rating with animation */}
          <div className="flex mb-4 sm:mb-6 gap-1">
            {[1, 2, 3, 4, 5].map((starNumber) => (
              <motion.div
                key={`star-${testimonial.id}-${starNumber}`}
                initial={{ scale: 0, rotate: -180 }}
                animate={{ scale: 1, rotate: 0 }}
                transition={{
                  duration: 0.5,
                  delay: index * 0.1 + starNumber * 0.1,
                  type: "spring",
                  stiffness: 200
                }}
                whileHover={{ scale: 1.2, rotate: 15 }}
              >
                <StarIcon
                  className={`h-4 w-4 sm:h-5 sm:w-5 lg:h-6 lg:w-6 transition-all duration-300 ${
                    starNumber <= testimonial.rating
                      ? "text-yellow-400 drop-shadow-lg filter brightness-110"
                      : "text-gray-600"
                  }`}
                />
              </motion.div>
            ))}
          </div>

          {/* Enhanced client info */}
          <div className="flex items-center">
            <motion.div
              className="relative w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 rounded-full overflow-hidden mr-3 sm:mr-4 border-2 border-gradient-to-r from-purple-500 to-cyan-500 p-[2px]"
              whileHover={{ scale: 1.1 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <div className="w-full h-full rounded-full overflow-hidden bg-gradient-to-r from-purple-500/20 to-cyan-500/20">
                <Image
                  src={testimonial.image}
                  alt={testimonial.name}
                  fill
                  className="object-cover"
                />
              </div>
            </motion.div>
            <div className="flex-1">
              <h4 className="text-white font-semibold text-base sm:text-lg mb-1 bg-gradient-to-r from-white to-gray-200 bg-clip-text text-transparent">
                {testimonial.name}
              </h4>
              <p className="text-gray-400 text-xs sm:text-sm font-medium">
                {testimonial.position}
              </p>
              <p className="text-purple-400 text-xs sm:text-sm font-medium">
                {testimonial.company}
              </p>
            </div>
          </div>
        </div>

        {/* Floating particles effect */}
        {isActive && (
          <div className="absolute inset-0 pointer-events-none">
            {[...Array(6)].map((_, i) => (
              <motion.div
                key={`particle-${testimonial.id}-${i}`}
                className="absolute w-1 h-1 bg-gradient-to-r from-purple-400 to-cyan-400 rounded-full"
                style={{
                  left: `${20 + i * 15}%`,
                  top: `${10 + i * 10}%`,
                }}
                animate={{
                  y: [-10, -20, -10],
                  opacity: [0.3, 0.8, 0.3],
                  scale: [0.5, 1, 0.5],
                }}
                transition={{
                  duration: 3 + i * 0.5,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: i * 0.3,
                }}
              />
            ))}
          </div>
        )}
      </div>
    </motion.div>
  );
};

export const Testimonials = () => {
  const sectionRef = useRef(null);
  const isInView = useInView(sectionRef, { once: true, amount: 0.2 });
  const [activeIndex, setActiveIndex] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const progressBarRef = useRef<HTMLDivElement>(null);
  const progressAnimationRef = useRef<Animation | null>(null);
  
  const testimonials: TestimonialType[] = [
    {
      id: 1,
      name: "王OK",
      position: "Singer",
      company: "88rising",
      testimonial: "The team at Oyu Mind was incredibly professional and a pleasure to work with. They took the time to understand our business needs and delivered a website that exceeded our expectations. Our online presence has grown significantly since the launch, and we&apos;ve received numerous compliments on the design and functionality. Highly recommend!",
      image: "/testimonials/person.jpg",
      rating: 5
    },
    {
      id: 2,
      name: "Ahmet YURDAKUL",
      position: "Data scientist",
      company: "HubX",
      testimonial: "The AI solution provided by Oyu Intelligence has automated several of our repetitive tasks, freeing up our team to focus on more strategic work. The implementation was seamless, and the support team has been fantastic in addressing any questions we&apos;ve had. This has been a game-changer for our productivity.",
      image: "/testimonials/data.jpg",
      rating: 5
    },
    {
      id: 3,
      name: "Uuganbayar",
      position: "Content Manager",
      company: "PrimeStore",
      testimonial: "Хамтарч ажиллахад тухай, гол нь маш чанартай үйлчилгээтэй байлаа. Чанарын хувьд Монголдоо байхааргүй өндөр чадварлаг залуус байна.",
      image: "/testimonials/3.png",
      rating: 5
    },
    {
      id: 4,
      name: "David Wilson",
      position: "Founder",
      company: "StartUp Ventures",
      testimonial: "As a startup founder, finding the right technology partner was crucial. Oyu Intelligence understood our vision and helped us build a solid foundation for our digital presence. Their expertise across multiple domains was invaluable.",
      image: "/testimonials/4.jpg",
      rating: 5
    },
  ];

  // Function to start auto-rotation
  const startAutoRotation = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    // Start progress bar animation
    if (progressBarRef.current) {
      const keyframes = [
        { width: '0%' },
        { width: '100%' }
      ];

      const options: KeyframeAnimationOptions = {
        duration: 7000, // 7 seconds for the progress bar
        easing: 'linear',
        fill: 'forwards'
      };

      progressAnimationRef.current = progressBarRef.current.animate(keyframes, options);
    }

    // Set interval for changing testimonials
    intervalRef.current = setInterval(() => {
      if (!isPaused && !isTransitioning) {
        setActiveIndex((prevIndex) => (prevIndex + 1) % testimonials.length);
      }
    }, 7000); // Change testimonial every 7 seconds
  }, [isPaused, isTransitioning, testimonials.length]);

  // Function to handle testimonial navigation
  const handleTestimonialChange = useCallback((index: number) => {
    if (isTransitioning) return;

    setIsTransitioning(true);
    setActiveIndex(index);

    // Reset the auto-rotation timer when manually changing testimonials
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    // Reset progress animation
    if (progressAnimationRef.current) {
      progressAnimationRef.current.cancel();
    }

    // Allow time for transition animation
    setTimeout(() => {
      setIsTransitioning(false);
      startAutoRotation();
    }, 600);
  }, [isTransitioning, startAutoRotation]);

  // Function to move to the next testimonial
  const goToNextTestimonial = useCallback(() => {
    handleTestimonialChange((activeIndex + 1) % testimonials.length);
  }, [activeIndex, testimonials.length, handleTestimonialChange]);

  // Function to move to the previous testimonial
  const goToPrevTestimonial = useCallback(() => {
    handleTestimonialChange((activeIndex - 1 + testimonials.length) % testimonials.length);
  }, [activeIndex, testimonials.length, handleTestimonialChange]);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'ArrowRight') {
        goToNextTestimonial();
      } else if (e.key === 'ArrowLeft') {
        goToPrevTestimonial();
      }
    };

    window.addEventListener('keydown', handleKeyDown);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [goToNextTestimonial, goToPrevTestimonial]);

  // Start auto-rotation when component mounts and is in view
  useEffect(() => {
    if (isInView && !isPaused && !isTransitioning) {
      startAutoRotation();
    }

    // Clean up interval on unmount
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }

      if (progressAnimationRef.current) {
        progressAnimationRef.current.cancel();
      }
    };
  }, [isInView, isPaused, isTransitioning, startAutoRotation]);

  return (
    <section
      id="testimonials"
      ref={sectionRef}
      className="relative flex flex-col items-center justify-center py-12 sm:py-16 lg:py-20 px-4 sm:px-6 lg:px-8 overflow-hidden"
    >
      {/* Mouse event handler div - removed z-10 to prevent it from blocking clicks */}
      <div
        className="absolute inset-0"
        onMouseEnter={() => setIsPaused(true)}
        onMouseLeave={() => setIsPaused(false)}
        aria-hidden="true"
      >
      </div>

      {/* Background elements */}
      <div className="absolute top-1/4 right-0 w-72 h-72 bg-purple-900/20 rounded-full blur-3xl -z-10"></div>
      <div className="absolute bottom-1/4 left-0 w-72 h-72 bg-cyan-900/20 rounded-full blur-3xl -z-10"></div>

      <SectionTitle
        subtitle="Client Feedback"
        title="What Our Clients Say"
        highlightWord="Clients"
        description="Don't just take our word for it. Here's what our clients have to say about their experience working with Oyu Intelligence."
        size="large"
        alignment="center"
        icon={
          <StarIcon className="w-5 h-5" />
        }
      />

      {/* Testimonials carousel */}
      <div className="w-full max-w-7xl">
        {/* Desktop view - carousel */}
        <div className="hidden md:block relative">
          <div className="flex justify-center">
            <AnimatePresence mode="wait">
              <motion.div
                key={activeIndex}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                transition={{ duration: 0.5 }}
                className="w-full max-w-3xl"
              >
                <TestimonialCard
                  testimonial={testimonials[activeIndex]}
                  index={0}
                  isActive={true}
                />
              </motion.div>
            </AnimatePresence>
          </div>

          {/* Desktop navigation arrows - removed pointer-events-none from parent */}
          <div className="absolute top-1/2 left-0 right-0 -translate-y-1/2 flex justify-between px-4 z-20">
            <motion.button
              whileHover={{ scale: 1.1, backgroundColor: "rgba(30,10,60,0.8)" }}
              whileTap={{ scale: 0.9 }}
              onClick={goToPrevTestimonial}
              className="p-3 rounded-full bg-[rgba(30,10,60,0.5)] border border-[#7042F88B] shadow-lg hover:border-purple-500 transition-colors"
              aria-label="Previous testimonial"
              disabled={isTransitioning}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </motion.button>

            <motion.button
              whileHover={{ scale: 1.1, backgroundColor: "rgba(30,10,60,0.8)" }}
              whileTap={{ scale: 0.9 }}
              onClick={goToNextTestimonial}
              className="p-3 rounded-full bg-[rgba(30,10,60,0.5)] border border-[#7042F88B] shadow-lg hover:border-purple-500 transition-colors"
              aria-label="Next testimonial"
              disabled={isTransitioning}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </motion.button>
          </div>

          {/* Testimonial indicators */}
          <div className="flex justify-center mt-8 space-x-2">
            {testimonials.map((testimonial) => (
              <button
                key={`indicator-${testimonial.id}`}
                onClick={() => handleTestimonialChange(testimonial.id - 1)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  testimonial.id - 1 === activeIndex
                    ? "bg-gradient-to-r from-purple-500 to-cyan-500 w-6"
                    : "bg-gray-600 hover:bg-gray-500"
                }`}
                aria-label={`View testimonial ${testimonial.id}`}
                disabled={isTransitioning}
              />
            ))}
          </div>
        </div>

        {/* Mobile view - single card with animation */}
        <div className="md:hidden w-full">
          <AnimatePresence mode="wait">
            <motion.div
              key={activeIndex}
              initial={{ opacity: 0, x: 100 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -100 }}
              transition={{ duration: 0.5 }}
              className="w-full"
            >
              <TestimonialCard
                testimonial={testimonials[activeIndex]}
                index={0}
                isActive={true}
              />
            </motion.div>
          </AnimatePresence>

          {/* Navigation arrows for mobile */}
          <div className="flex justify-between mt-4 z-20">
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={goToPrevTestimonial}
              className="p-2 rounded-full bg-[rgba(30,10,60,0.5)] border border-[#7042F88B] hover:border-purple-500 transition-colors shadow-md"
              aria-label="Previous testimonial"
              disabled={isTransitioning}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </motion.button>

            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={goToNextTestimonial}
              className="p-2 rounded-full bg-[rgba(30,10,60,0.5)] border border-[#7042F88B] hover:border-purple-500 transition-colors shadow-md"
              aria-label="Next testimonial"
              disabled={isTransitioning}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </motion.button>
          </div>
        </div>
      </div>

      {/* Progress bar */}
      <div className="w-full max-w-md mt-10 relative h-1 bg-gray-700 rounded-full overflow-hidden">
        <div
          ref={progressBarRef}
          className="absolute top-0 left-0 h-full w-0 bg-gradient-to-r from-purple-500 to-cyan-500"
        />

        {/* Indicator dots */}
        <div className="absolute top-0 left-0 w-full h-full flex justify-between px-1">
          {testimonials.map((testimonial) => (
            <div
              key={`dot-${testimonial.id}`}
              className={`w-1 h-1 rounded-full mt-0 ${
                testimonial.id - 1 === activeIndex
                  ? "bg-white"
                  : "bg-gray-500"
              } transition-colors duration-300`}
            />
          ))}
        </div>
      </div>
    </section>
  );
};
