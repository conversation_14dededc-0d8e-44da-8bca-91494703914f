# Email Service API

This is a Python Flask API that handles email sending for the Oyu Intelligence contact form using SendGrid.

## Setup Instructions

### Prerequisites
- Python 3.7 or higher
- pip (Python package installer)

### Installation

1. **Navigate to the API directory:**
   ```bash
   cd api
   ```

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Start the email service:**
   ```bash
   python start_email_service.py
   ```
   
   Or run directly:
   ```bash
   python email_service.py
   ```

The API will start on `http://localhost:5000`

### API Endpoints

#### Health Check
- **URL:** `GET /health`
- **Description:** Check if the service is running
- **Response:** 
  ```json
  {
    "status": "healthy",
    "timestamp": "2024-01-01T12:00:00",
    "service": "email-service"
  }
  ```

#### Send Email
- **URL:** `POST /send-email`
- **Description:** Send contact form email via Send<PERSON><PERSON>
- **Request Body:**
  ```json
  {
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "subject": "Contact Form Inquiry",
    "message": "Hello, I would like to know more about your services."
  }
  ```
- **Success Response:**
  ```json
  {
    "success": true,
    "message": "Email sent successfully",
    "status_code": 202
  }
  ```
- **Error Response:**
  ```json
  {
    "error": "Failed to send email",
    "details": "Error description"
  }
  ```

### Configuration

The SendGrid API key is currently hardcoded in `email_service.py`. For production deployment, consider using environment variables:

```python
SENDGRID_API_KEY = os.getenv('SENDGRID_API_KEY', 'your-default-key')
```

### Email Configuration
- **From Email:** <EMAIL>
- **To Email:** <EMAIL>
- **SendGrid API Key:** Configured in the service

### Testing

You can test the API using curl:

```bash
# Health check
curl http://localhost:5000/health

# Send test email
curl -X POST http://localhost:5000/send-email \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test User",
    "email": "<EMAIL>",
    "subject": "Test Subject",
    "message": "This is a test message"
  }'
```

### Troubleshooting

1. **Port already in use:** If port 5000 is busy, modify the port in `email_service.py`
2. **SendGrid errors:** Check the API key and ensure the sender email is verified
3. **CORS issues:** The API includes CORS headers for cross-origin requests

### Security Notes

- Input validation and sanitization are implemented
- Email format validation is performed
- Content length limits are enforced
- HTML/script tag removal for security
