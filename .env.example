# Environment Variables Configuration
# Copy this file to .env.local and fill in your actual values

# Next.js Configuration
PYTHON_API_URL=http://localhost:5000

# SendGrid Email Service Configuration
# Get your API key from: https://app.sendgrid.com/settings/api_keys
SENDGRID_API_KEY=your_sendgrid_api_key_here

# Email Configuration
# FROM_EMAIL must be verified in SendGrid dashboard
FROM_EMAIL=<EMAIL>
TO_EMAIL=<EMAIL>

# Development/Testing Configuration
# Set to 'true' for testing mode (no actual emails sent)
# Set to 'false' for production (real emails sent)
TEST_MODE=false

# Optional: Custom Port Configuration
# EMAIL_SERVICE_PORT=5000
# NEXT_PORT=3000
