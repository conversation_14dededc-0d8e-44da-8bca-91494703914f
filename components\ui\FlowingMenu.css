.menu-wrap {
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: 1rem;
  backdrop-filter: blur(20px);
  background: rgba(3, 0, 20, 0.4);
  border: 1px solid rgba(112, 66, 248, 0.3);
}

.menu {
  display: flex;
  flex-direction: column;
  height: 100%;
  margin: 0;
  padding: 0;
}

.menu__item {
  flex: 1;
  position: relative;
  overflow: hidden;
  text-align: center;
  box-shadow: 0 -1px rgba(112, 66, 248, 0.2);
  transition: all 0.3s ease;
}

.menu__item:hover {
  box-shadow: 0 -1px rgba(112, 66, 248, 0.6);
}

.menu__item-link {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  position: relative;
  cursor: pointer;
  text-transform: uppercase;
  text-decoration: none;
  white-space: nowrap;
  font-weight: 600;
  color: #e2e8f0;
  font-size: clamp(1.5rem, 3vh, 2.5rem);
  background: linear-gradient(135deg, rgba(147, 51, 234, 0.1), rgba(6, 182, 212, 0.1));
  transition: all 0.4s ease;
  letter-spacing: 0.05em;
}

.menu__item-link:hover {
  color: #030014;
  background: linear-gradient(135deg, rgba(147, 51, 234, 0.2), rgba(6, 182, 212, 0.2));
}

.menu__item-link:focus:not(:focus-visible) {
  color: #e2e8f0;
}

.marquee {
  position: absolute;
  top: 0;
  left: 0;
  overflow: hidden;
  width: 100%;
  height: 100%;
  pointer-events: none;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(240, 240, 255, 0.9));
  transform: translate3d(0, 101%, 0);
  transition: transform 0.6s cubic-bezier(0.25, 0.1, 0.25, 1);
  backdrop-filter: blur(10px);
  border-radius: inherit;
}

.marquee__inner-wrap {
  height: 100%;
  width: 200%;
  display: flex;
  transform: translateX(0);
}

.marquee__inner {
  display: flex;
  align-items: center;
  position: relative;
  height: 100%;
  width: 200%;
  will-change: transform;
  animation: marquee 20s linear infinite;
}

.marquee span {
  color: #030014;
  white-space: nowrap;
  text-transform: uppercase;
  font-weight: 600;
  font-size: clamp(1.2rem, 2.5vh, 2rem);
  line-height: 1.2;
  padding: 1vh 1vw 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.marquee__img {
  width: clamp(150px, 15vw, 200px);
  height: clamp(60px, 6vh, 80px);
  margin: 1em 2vw;
  padding: 0.5em 0;
  border-radius: 1rem;
  background-size: cover;
  background-position: 50% 50%;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  border: 2px solid rgba(147, 51, 234, 0.3);
  transition: all 0.3s ease;
}

.marquee__img:hover {
  transform: scale(1.05);
  border-color: rgba(6, 182, 212, 0.5);
}

.menu__item-link:hover + .marquee {
  transform: translate3d(0, 0%, 0);
}

@keyframes marquee {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(-50%);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .menu__item-link {
    font-size: clamp(1rem, 2.5vh, 1.8rem);
  }
  
  .marquee span {
    font-size: clamp(0.9rem, 2vh, 1.5rem);
  }
  
  .marquee__img {
    width: clamp(120px, 20vw, 150px);
    height: clamp(50px, 5vh, 60px);
    margin: 0.5em 1vw;
  }
}

/* Enhanced glow effects */
.menu__item:nth-child(1) .menu__item-link:hover {
  box-shadow: 0 0 20px rgba(147, 51, 234, 0.4);
}

.menu__item:nth-child(2) .menu__item-link:hover {
  box-shadow: 0 0 20px rgba(6, 182, 212, 0.4);
}

.menu__item:nth-child(3) .menu__item-link:hover {
  box-shadow: 0 0 20px rgba(236, 72, 153, 0.4);
}

.menu__item:nth-child(4) .menu__item-link:hover {
  box-shadow: 0 0 20px rgba(34, 197, 94, 0.4);
}
