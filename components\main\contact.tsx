"use client";

import { EnvelopeIcon, MapPinIcon, PhoneIcon } from "@heroicons/react/24/outline";
import { motion, useInView } from "framer-motion";
import { useRef, useState } from "react";

import { GlowingEffect } from "@/components/ui/glowing-effect";
import {
  slideInFromLeft,
  slideInFromRight,
  slideInFromTop,
} from "@/lib/motion";

// Contact info card component
const ContactInfoCard = ({
  icon: Icon,
  title,
  details,
  delay
}: {
  icon: any;
  title: string;
  details: string;
  delay: number;
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay }}
      className="relative flex items-start p-6 bg-gradient-to-br from-[rgba(30,10,60,0.4)] via-[rgba(15,5,40,0.5)] to-[rgba(3,0,20,0.6)] border border-[#7042F88B] rounded-xl mb-6 hover:border-purple-500/50 transition-all duration-500 group"
    >
      <GlowingEffect
        spread={100}
        glow={true}
        disabled={false}
        proximity={130}
        inactiveZone={0.05}
        blur={4}
        borderWidth={3}
      />
      <motion.div
        className="bg-gradient-to-br from-purple-600 to-cyan-600 p-4 rounded-xl mr-6 relative z-10 group-hover:scale-110 transition-transform duration-300"
        whileHover={{ rotate: 5 }}
      >
        <Icon className="h-7 w-7 text-white" />
      </motion.div>
      <div className="relative z-10 flex-1">
        <motion.h3
          className="text-white font-semibold mb-2 text-lg group-hover:text-cyan-400 transition-colors duration-300"
          whileHover={{ scale: 1.02 }}
        >
          {title}
        </motion.h3>
        <p className="text-gray-300 text-sm leading-relaxed">{details}</p>
      </div>
    </motion.div>
  );
};

// Input field component with animation
const AnimatedInput = ({
  label,
  type,
  name,
  value,
  onChange,
  required = true,
  as = "input",
  rows = 5,
  delay
}: {
  label: string;
  type?: string;
  name: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  required?: boolean;
  as?: "input" | "textarea";
  rows?: number;
  delay: number;
}) => {
  const [isFocused, setIsFocused] = useState(false);

  const inputClasses = `w-full px-4 py-3 bg-[rgba(3,0,20,0.5)] border ${
    isFocused ? "border-purple-500" : "border-[#7042F88B]"
  } rounded-lg text-white focus:outline-none focus:border-purple-500 transition-colors`;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay }}
      className="mb-6"
    >
      <label htmlFor={name} className="block text-gray-300 mb-2 text-sm font-medium">
        {label}
      </label>
      {as === "input" ? (
        <input
          type={type ?? "text"}
          id={name}
          name={name}
          value={value}
          onChange={onChange}
          required={required}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          className={inputClasses}
        />
      ) : (
        <textarea
          id={name}
          name={name}
          value={value}
          onChange={onChange}
          required={required}
          rows={rows}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          className={inputClasses}
        ></textarea>
      )}
    </motion.div>
  );
};

export const Contact = () => {
  const sectionRef = useRef(null);
  const isInView = useInView(sectionRef, { once: true, amount: 0.2 });

  const [formData, setFormData] = useState({
    name: "",
    email: "",
    subject: "",
    message: "",
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    // Clear error when user starts typing
    if (submitError) {
      setSubmitError(null);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    console.log('🚀 Form submission started');
    console.log('📝 Form data:', formData);

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      // Validate form data
      console.log('✅ Starting validation...');
      if (!formData.name.trim() || !formData.email.trim() ||
          !formData.subject.trim() || !formData.message.trim()) {
        throw new Error('Please fill in all fields');
      }

      // Email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(formData.email)) {
        throw new Error('Please enter a valid email address');
      }

      console.log('✅ Validation passed, sending to API...');

      // Submit to API
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      console.log('📡 API Response status:', response.status);
      const result = await response.json();
      console.log('📡 API Response data:', result);

      if (!response.ok) {
        throw new Error(result.error ?? 'Failed to send message');
      }

      // Success
      console.log('🎉 Email sent successfully!');

      // Check if it's demo mode or fallback
      if (result.demo) {
        console.log('ℹ️ Demo mode: Python API not configured');
      } else if (result.fallback) {
        console.log('⚠️ Fallback mode: Python API unavailable');
      }

      setSubmitSuccess(true);
      setFormData({ name: "", email: "", subject: "", message: "" });

      // Reset success message after 5 seconds
      setTimeout(() => {
        setSubmitSuccess(false);
      }, 5000);

    } catch (error) {
      console.error('❌ Form submission error:', error);
      setSubmitError(error instanceof Error ? error.message : 'Failed to send message');
    } finally {
      setIsSubmitting(false);
      console.log('🏁 Form submission completed');
    }
  };

  return (
    <section
      id="contact"
      ref={sectionRef}
      className="relative flex flex-col items-center justify-center py-12 sm:py-16 lg:py-20 px-4 sm:px-6 lg:px-8 overflow-hidden"
    >
      {/* Background elements */}
      <div className="absolute top-1/3 left-0 w-72 h-72 bg-purple-900/20 rounded-full blur-3xl"></div>
      <div className="absolute bottom-1/3 right-0 w-72 h-72 bg-cyan-900/20 rounded-full blur-3xl"></div>

      <motion.div
        variants={slideInFromTop}
        initial="hidden"
        animate={isInView ? "visible" : "hidden"}
        className="Welcome-box py-[8px] px-[7px] border border-[#7042f88b] opacity-[0.9]] mb-6"
      >
        <h1 className="Welcome-text text-[13px]">
          Get In Touch
        </h1>
      </motion.div>

      <motion.h2
        variants={slideInFromLeft(0.5)}
        initial="hidden"
        animate={isInView ? "visible" : "hidden"}
        className="text-4xl font-bold text-white mb-4 text-center"
      >
        Contact <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-500 to-cyan-500">Us</span>
      </motion.h2>

      <motion.p
        variants={slideInFromRight(0.5)}
        initial="hidden"
        animate={isInView ? "visible" : "hidden"}
        className="text-gray-400 text-center max-w-2xl mb-16"
      >
        Have a project in mind or want to learn more about our services?
        We&apos;d love to hear from you. Reach out and let&apos;s start a conversation.
      </motion.p>

      <div className="w-full max-w-6xl flex flex-col lg:flex-row gap-8 sm:gap-10 lg:gap-12">
        {/* Contact Information */}
        <div className="w-full lg:w-1/3">
          <motion.h3
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.5 }}
            className="text-2xl font-bold text-white mb-6"
          >
            Contact Information
          </motion.h3>

          <ContactInfoCard
            icon={PhoneIcon}
            title="Phone"
            details="86970213"
            delay={0.2}
          />

          <ContactInfoCard
            icon={EnvelopeIcon}
            title="Email"
            details="<EMAIL>"
            delay={0.3}
          />

          <ContactInfoCard
            icon={MapPinIcon}
            title="Office"
            details="Sambuu St 47, CHD - 5 khoroo, Ulaanbaatar 15171"
            delay={0.4}
          />

          {/* Map Placeholder */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.5, delay: 0.5 }}
            className="mt-8 relative h-[200px] rounded-lg overflow-hidden border border-[#7042F88B]"
          >
            <GlowingEffect
              spread={100}
              glow={true}
              disabled={false}
              proximity={130}
              inactiveZone={0.1}
              blur={3}
              borderWidth={2}
            />
            <div className="absolute inset-0 bg-gradient-to-br from-[#1E0E3E] to-[#030014] flex flex-col items-center justify-center p-4 z-10">
              <div className="w-12 h-12 bg-gradient-to-r from-purple-600 to-cyan-600 rounded-full flex items-center justify-center mb-3">
                <MapPinIcon className="h-6 w-6 text-white" />
              </div>
              <span className="text-white font-medium text-center">Sambuu St 47, CHD - 5 khoroo, Ulaanbaatar 15171</span>
              <a
                href="https://maps.app.goo.gl/yj1MUxqiMvf6hha69"
                target="_blank"
                rel="noopener noreferrer"
                className="mt-4 px-4 py-2 text-sm bg-[rgba(255,255,255,0.1)] text-white rounded-lg hover:bg-[rgba(255,255,255,0.2)] transition-colors inline-flex items-center"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                </svg>
                Open in Google Maps
              </a>
            </div>
          </motion.div>
        </div>

        {/* Contact Form */}
        <div className="relative w-full lg:w-2/3 bg-[rgba(3,0,20,0.3)] p-8 rounded-xl border border-[#7042F88B]">
          <GlowingEffect
            spread={120}
            glow={true}
            disabled={false}
            proximity={150}
            inactiveZone={0.05}
            blur={4}
            borderWidth={3}
          />
          {submitSuccess ? (
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className="relative z-10 bg-gradient-to-r from-green-500/20 to-cyan-500/20 border border-green-500 text-green-300 p-6 rounded-lg text-center h-full flex flex-col items-center justify-center"
            >
              <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-white mb-2">Message Sent!</h3>
              <p className="text-gray-300 mb-6">Thank you for reaching out. We&apos;ll get back to you as soon as possible.</p>
              <button
                onClick={() => setSubmitSuccess(false)}
                className="px-6 py-2 button-primary text-white rounded-lg hover:opacity-90 transition-opacity"
              >
                Send Another Message
              </button>
            </motion.div>
          ) : (
            <form onSubmit={handleSubmit} className="relative z-10 space-y-2">
              {submitError && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-red-500/20 border border-red-500 text-red-300 p-4 rounded-lg mb-4"
                >
                  <div className="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    {submitError}
                  </div>
                </motion.div>
              )}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <AnimatedInput
                  label="Your Name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  delay={0.2}
                />

                <AnimatedInput
                  label="Your Email"
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  delay={0.3}
                />
              </div>

              <AnimatedInput
                label="Subject"
                name="subject"
                value={formData.subject}
                onChange={handleChange}
                delay={0.4}
              />

              <AnimatedInput
                label="Your Message"
                name="message"
                value={formData.message}
                onChange={handleChange}
                as="textarea"
                rows={6}
                delay={0.5}
              />

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                transition={{ duration: 0.5, delay: 0.6 }}
              >
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full py-4 mt-4 bg-gradient-to-r from-purple-600 to-cyan-600 text-white font-medium rounded-lg flex items-center justify-center hover:opacity-90 transition-opacity"
                >
                  {isSubmitting ? (
                    <div className="flex items-center">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Sending Message...
                    </div>
                  ) : (
                    <div className="flex items-center">
                      <span>Send Message</span>
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                      </svg>
                    </div>
                  )}
                </button>
              </motion.div>
            </form>
          )}
        </div>
      </div>
    </section>
  );
};
