{"name": "space-portfolio", "version": "1.0.1", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "email-service": "cd api && python email_service.py", "dev:full": "concurrently \"npm run dev\" \"npm run email-service\"", "setup:email": "cd api && pip install -r requirements.txt", "dev:start": "bash scripts/start-dev.sh", "dev:start:windows": "scripts/start-dev.bat"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/sanidhyy"}, "description": "Welcome to my full stack Next.js 14 space portfolio.", "keywords": ["reactjs", "nextjs", "vercel", "react", "space-portfolio", "portfolio", "react-icons", "cn", "clsx", "3d-portfolio", "3d-website", "sonner", "framer-motion", "motion", "animation", "heroicons", "next-themes", "postcss", "prettier", "react-dom", "tailwindcss", "tailwindcss-animate", "ui/ux", "js", "javascript", "typescript", "eslint", "html", "css"], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sanidhyy/space-portfolioit"}, "homepage": "https://github.com/sanidhyy/space-portfolio#readme", "bugs": {"url": "https://github.com/sanidhyy/space-portfolio/issues", "email": "<EMAIL>"}, "funding": [{"type": "patreon", "url": "https://www.patreon.com/sanidhy"}, {"type": "Buy me a coffee", "url": "https://www.buymeacoffee.com/sanidhy"}], "dependencies": {"@heroicons/react": "^2.1.1", "@react-three/drei": "^9.93.0", "@react-three/fiber": "^8.15.13", "@tsparticles/engine": "^3.8.1", "@tsparticles/react": "^3.0.0", "@tsparticles/slim": "^3.8.1", "clsx": "^2.1.0", "framer-motion": "^10.17.12", "gsap": "^3.13.0", "locomotive-scroll": "^4.1.4", "lucide-react": "^0.511.0", "motion": "^12.14.0", "next": "14.2.15", "ogl": "^1.0.11", "react": "^18", "react-dom": "^18", "react-icons": "^5.0.1", "react-intersection-observer": "^9.5.3", "react-markdown": "^10.1.0", "tailwind-merge": "^2.2.0", "three": "^0.160.0"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/three": "^0.160.0", "autoprefixer": "^10.0.1", "concurrently": "^9.1.2", "eslint": "^8", "eslint-config-next": "14.0.4", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}