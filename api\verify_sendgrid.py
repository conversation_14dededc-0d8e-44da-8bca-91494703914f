#!/usr/bin/env python3
"""
SendGrid Verification Helper Script
This script helps verify your SendGrid setup step by step
"""

import sendgrid
from sendgrid.helpers.mail import Mail
import sys
import os

# Try to load environment variables from .env.local
try:
    from dotenv import load_dotenv
    load_dotenv('../.env.local')
except ImportError:
    print("Note: python-dotenv not installed. Using system environment variables.")

# SendGrid configuration from environment
SENDGRID_API_KEY = os.getenv('SENDGRID_API_KEY')
FROM_EMAIL = os.getenv('FROM_EMAIL', '<EMAIL>')
TO_EMAIL = os.getenv('TO_EMAIL', '<EMAIL>')

def check_api_key():
    """Check if API key is valid"""
    print("🔑 Checking SendGrid API Key...")
    try:
        sg = sendgrid.SendGridAPIClient(api_key=SENDGRID_API_KEY)
        # Try to get API key info (this will fail if key is invalid)
        response = sg.client.user.get()
        print(f"✅ API Key is valid")
        return True
    except Exception as e:
        print(f"❌ API Key is invalid: {str(e)}")
        return False

def check_sender_verification():
    """Check sender verification status"""
    print("📧 Checking Sender Verification...")
    try:
        sg = sendgrid.SendGridAPIClient(api_key=SENDGRID_API_KEY)
        
        # Try to send a test email to check verification
        message = Mail(
            from_email=FROM_EMAIL,
            to_emails=TO_EMAIL,
            subject='SendGrid Verification Test',
            html_content='<p>This is a verification test.</p>'
        )
        
        response = sg.send(message)
        
        if response.status_code == 202:
            print(f"✅ Sender email ({FROM_EMAIL}) is verified and working!")
            return True
        else:
            print(f"⚠️ Unexpected response: {response.status_code}")
            return False
            
    except Exception as e:
        error_msg = str(e)
        if "does not match a verified Sender Identity" in error_msg:
            print(f"❌ Sender email ({FROM_EMAIL}) is NOT verified")
            print("📋 To verify your sender email:")
            print("   1. Go to https://app.sendgrid.com/")
            print("   2. Navigate to Settings → Sender Authentication")
            print("   3. Click 'Single Sender Verification'")
            print("   4. Add <NAME_EMAIL>")
            return False
        else:
            print(f"❌ Error checking sender: {error_msg}")
            return False

def main():
    """Main verification process"""
    print("🧪 SendGrid Setup Verification")
    print("=" * 50)
    
    # Step 1: Check API Key
    if not check_api_key():
        print("\n❌ Setup Failed: Invalid API Key")
        sys.exit(1)
    
    print()
    
    # Step 2: Check Sender Verification
    if not check_sender_verification():
        print("\n⚠️ Setup Incomplete: Sender email needs verification")
        print("\n📝 Next Steps:")
        print("1. Verify your sender email in SendGrid dashboard")
        print("2. Run this script again to confirm")
        print("3. Set TEST_MODE = False in email_service.py")
        sys.exit(1)
    
    print("\n🎉 SendGrid Setup Complete!")
    print("✅ API Key is valid")
    print("✅ Sender email is verified")
    print("✅ Ready to send emails")
    
    print("\n📝 Final Steps:")
    print("1. Set TEST_MODE = False in api/email_service.py")
    print("2. Restart the email service")
    print("3. Test the contact form on your website")

if __name__ == "__main__":
    main()
