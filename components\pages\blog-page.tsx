"use client";

import { motion, useInView } from "framer-motion";
import Image from "next/image";
import Link from "next/link";
import { useRef, useState } from "react";

import { SectionTitle } from "@/components/ui/section-title";
import { BLOG_POSTS } from "@/constants/blog";
import { BlogPostType } from "@/types";

// Blog card component with enhanced animations
const BlogCard = ({
  post,
  index
}: {
  post: BlogPostType;
  index: number;
}) => {
  const cardRef = useRef(null);
  const isInView = useInView(cardRef, { once: true, margin: "-100px" });

  // Animation variants for card elements
  const cardVariants = {
    hidden: {
      opacity: 0,
      y: 50,
      scale: 0.95
    },
    visible: (i: number) => ({
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        type: "spring",
        damping: 12,
        stiffness: 100,
        delay: i * 0.1,
        duration: 0.6
      }
    }),
    hover: {
      y: -10,
      boxShadow: "0 20px 25px -5px rgba(124, 58, 237, 0.1), 0 10px 10px -5px rgba(124, 58, 237, 0.04)",
      borderColor: "rgba(139, 92, 246, 0.5)",
      transition: {
        type: "spring",
        stiffness: 400,
        damping: 10
      }
    }
  };

  const imageVariants = {
    hover: {
      scale: 1.1,
      transition: {
        duration: 0.4,
        ease: "easeOut"
      }
    }
  };

  const categoryVariants = {
    hidden: { opacity: 0, x: 20 },
    visible: (i: number) => ({
      opacity: 1,
      x: 0,
      transition: {
        delay: i * 0.1 + 0.2,
        duration: 0.5
      }
    }),
    hover: {
      scale: 1.05,
      boxShadow: "0 4px 6px -1px rgba(124, 58, 237, 0.2)",
      transition: {
        duration: 0.2
      }
    }
  };

  const contentVariants = {
    hidden: { opacity: 0 },
    visible: (i: number) => ({
      opacity: 1,
      transition: {
        delay: i * 0.1 + 0.3,
        duration: 0.5
      }
    })
  };

  const arrowVariants = {
    hover: {
      x: 5,
      transition: {
        type: "spring",
        stiffness: 400
      }
    }
  };

  return (
    <motion.div
      ref={cardRef}
      custom={index}
      initial="hidden"
      animate={isInView ? "visible" : "hidden"}
      whileHover="hover"
      variants={cardVariants}
      className="group"
    >
      <Link href={`/blog/${post.slug}`} className="block">
        <motion.div
          className="bg-[rgba(15,5,30,0.4)] rounded-xl border border-purple-500/20 overflow-hidden h-full flex flex-col shadow-lg shadow-purple-900/10"
        >
          {/* Image */}
          <div className="relative h-52 w-full overflow-hidden">
            <motion.div variants={imageVariants}>
              <Image
                src={post.image}
                alt={post.title}
                fill
                className="object-cover"
              />
            </motion.div>
            <div className="absolute inset-0 bg-gradient-to-t from-[rgba(15,5,30,0.8)] to-transparent opacity-60 group-hover:opacity-40 transition-opacity duration-300"></div>
            <motion.div
              className="absolute top-4 right-4 bg-gradient-to-r from-purple-600 to-purple-500 text-white text-xs px-3 py-1.5 rounded-full shadow-md"
              variants={categoryVariants}
              custom={index}
            >
              {post.category}
            </motion.div>
          </div>

          {/* Content */}
          <motion.div
            className="p-6 flex flex-col flex-grow"
            variants={contentVariants}
            custom={index}
          >
            <div className="flex items-center mb-4">
              <motion.div
                className="relative w-10 h-10 rounded-full overflow-hidden mr-3 border-2 border-purple-500/30"
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ delay: index * 0.1 + 0.4, duration: 0.3 }}
              >
                <Image
                  src={post.authorImage}
                  alt={post.author}
                  fill
                  className="object-cover"
                />
              </motion.div>
              <div className="text-sm">
                <span className="text-white font-medium">{post.author}</span>
                <div className="flex items-center text-gray-400">
                  <span>{post.date}</span>
                  <span className="mx-2">•</span>
                  <span>{post.readTime}</span>
                </div>
              </div>
            </div>

            <h3 className="text-xl font-bold text-white mb-3 group-hover:text-purple-300 transition-colors line-clamp-2">
              {post.title}
            </h3>

            <p className="text-gray-300 text-sm mb-5 flex-grow line-clamp-3">
              {post.excerpt}
            </p>

            <div className="mt-auto">
              <span className="text-purple-400 text-sm font-medium flex items-center group-hover:text-purple-300 transition-colors">
                Read article
                <motion.svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4 ml-2"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  variants={arrowVariants}
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                </motion.svg>
              </span>
            </div>
          </motion.div>
        </motion.div>
      </Link>
    </motion.div>
  );
};

export const BlogPage = () => {
  const [activeCategory, setActiveCategory] = useState("All");
  const [searchQuery, setSearchQuery] = useState("");

  // Get all unique categories
  const categories = ["All", ...Array.from(new Set(BLOG_POSTS.map(post => post.category)))];

  // Filter posts based on active category and search query
  const filteredPosts = BLOG_POSTS.filter(post => {
    const matchesCategory = activeCategory === "All" || post.category === activeCategory;
    const matchesSearch = searchQuery === "" ||
      post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      post.excerpt.toLowerCase().includes(searchQuery.toLowerCase());

    return matchesCategory && matchesSearch;
  });

  return (
    <div className="flex flex-col min-h-screen py-20 px-4 mt-16">
      <div className="max-w-7xl mx-auto w-full">
        {/* Header */}
        <div className="mb-16">
          <SectionTitle
            subtitle="Latest Insights"
            title="Our Blog"
            highlightWord="Blog"
            description="Stay updated with the latest trends, insights, and best practices in technology and digital innovation."
            size="large"
            alignment="center"
            icon={
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
              </svg>
            }
          />
        </div>

        {/* Search and filter */}
        <div className="flex flex-col md:flex-row justify-between items-center mb-16 gap-6">
          {/* Category filter */}
          <div className="flex flex-wrap justify-center gap-3">
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => setActiveCategory(category)}
                className={`px-5 py-2.5 rounded-full transition-all duration-300 text-sm font-medium ${
                  activeCategory === category
                    ? "bg-gradient-to-r from-purple-600 to-cyan-600 text-white shadow-lg shadow-purple-900/30"
                    : "bg-[rgba(15,5,30,0.4)] border border-purple-500/20 text-gray-200 hover:border-purple-500 hover:bg-[rgba(15,5,30,0.6)]"
                }`}
              >
                {category}
              </button>
            ))}
          </div>

          {/* Search */}
          <div className="relative w-full md:w-72">
            <input
              type="text"
              placeholder="Search articles..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full px-5 py-3 bg-[rgba(15,5,30,0.4)] border border-purple-500/20 rounded-lg text-white focus:outline-none focus:border-purple-500 pl-12 shadow-md"
            />
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 absolute left-4 top-1/2 transform -translate-y-1/2 text-purple-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
          </div>
        </div>

        {/* Blog posts grid */}
        {filteredPosts.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredPosts.map((post, index) => (
              <BlogCard
                key={post.id}
                post={post}
                index={index}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-20 bg-[rgba(15,5,30,0.3)] rounded-xl border border-purple-500/20 shadow-lg">
            <div className="w-16 h-16 mx-auto mb-6 bg-purple-900/30 rounded-full flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 className="text-2xl font-bold text-white mb-3">No articles found</h3>
            <p className="text-gray-300 mb-6 max-w-md mx-auto">We couldn&apos;t find any articles matching your search criteria. Try adjusting your filters or search terms.</p>
            <button
              onClick={() => {
                setSearchQuery('');
                setActiveCategory('All');
              }}
              className="px-6 py-2.5 bg-gradient-to-r from-purple-600 to-cyan-600 text-white rounded-lg hover:opacity-90 transition-all duration-300 inline-flex items-center shadow-md hover:shadow-purple-900/30"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Reset Filters
            </button>
          </div>
        )}

        {/* Back to Home Button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.5 }}
          className="mt-20 text-center"
        >
          <Link
            href="/"
            className="px-8 py-4 bg-gradient-to-r from-purple-600 to-cyan-600 text-white rounded-lg hover:opacity-90 transition-all duration-300 inline-flex items-center font-medium shadow-lg shadow-purple-900/20 hover:shadow-purple-800/30 hover:-translate-y-1"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Back to Home
          </Link>
        </motion.div>
      </div>
    </div>
  );
};
