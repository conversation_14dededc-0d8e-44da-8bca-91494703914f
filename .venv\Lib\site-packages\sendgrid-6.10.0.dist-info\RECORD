sendgrid-6.10.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
sendgrid-6.10.0.dist-info/LICENSE,sha256=GPrcjgXCIkbSqRZJvPlFyVj1gNctojvxSSy195mKXLU,1097
sendgrid-6.10.0.dist-info/METADATA,sha256=eY8NY8c6PKeKGz6Hvcqme7j17QXDryqgL-jeE2QB-mw,12104
sendgrid-6.10.0.dist-info/RECORD,,
sendgrid-6.10.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sendgrid-6.10.0.dist-info/WHEEL,sha256=pkctZYzUS4AYVn6dJ-7367OJZivF2e8RA9b_ZBjif18,92
sendgrid-6.10.0.dist-info/top_level.txt,sha256=eal4tbi_WImp6G0Aw1kh_sYXv2N4Kz-Bhz6rsKQVzf8,14
sendgrid/__init__.py,sha256=zfhI57Qk3GdhNpnPyVIcJP1oHWr0M4RKz5Rt4vgj3HU,834
sendgrid/__pycache__/__init__.cpython-313.pyc,,
sendgrid/__pycache__/base_interface.cpython-313.pyc,,
sendgrid/__pycache__/sendgrid.cpython-313.pyc,,
sendgrid/__pycache__/twilio_email.cpython-313.pyc,,
sendgrid/__pycache__/version.cpython-313.pyc,,
sendgrid/base_interface.py,sha256=024Ghg1msQLTcl0ZcRNJuirlbljCvjPLLFU--Uc5u74,2232
sendgrid/helpers/__init__.py,sha256=mOgK-c4P2b4e550UQElL1PXC9ZpQF7yA0HJ6JnshTUs,57
sendgrid/helpers/__pycache__/__init__.cpython-313.pyc,,
sendgrid/helpers/endpoints/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sendgrid/helpers/endpoints/__pycache__/__init__.cpython-313.pyc,,
sendgrid/helpers/endpoints/ip/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sendgrid/helpers/endpoints/ip/__pycache__/__init__.cpython-313.pyc,,
sendgrid/helpers/endpoints/ip/__pycache__/unassigned.cpython-313.pyc,,
sendgrid/helpers/endpoints/ip/unassigned.py,sha256=B5ikBCrgEyZXwbMRiQgpPVqYKvDBIf-5IzTJbxXnyL8,1673
sendgrid/helpers/eventwebhook/__init__.py,sha256=lZiXFE2ClfNsywFZNwcLBGqZ3tNUjPKS4mJStTKm6sU,1986
sendgrid/helpers/eventwebhook/__pycache__/__init__.cpython-313.pyc,,
sendgrid/helpers/eventwebhook/__pycache__/eventwebhook_header.cpython-313.pyc,,
sendgrid/helpers/eventwebhook/eventwebhook_header.py,sha256=5N2A4T7IzZO-JjWO3MEsCgzXMdw5tNUH00Kbw95UO0w,354
sendgrid/helpers/inbound/__init__.py,sha256=U7L9GXW3jxDynFmYEUBZPZcQ3dY0UE44Offdu6GEb_g,438
sendgrid/helpers/inbound/__pycache__/__init__.cpython-313.pyc,,
sendgrid/helpers/inbound/__pycache__/app.cpython-313.pyc,,
sendgrid/helpers/inbound/__pycache__/config.cpython-313.pyc,,
sendgrid/helpers/inbound/__pycache__/parse.cpython-313.pyc,,
sendgrid/helpers/inbound/__pycache__/send.cpython-313.pyc,,
sendgrid/helpers/inbound/app.py,sha256=7GZLmn6Sr8-q_Z6Xued7TSRdXF-Yc1rk2Liz_PaUkD0,1203
sendgrid/helpers/inbound/config.py,sha256=p6gT-nPVxkiSnKGE80EdjeKlXVPUFtdNq_bvJWPgad4,2133
sendgrid/helpers/inbound/config.yml,sha256=qTc8tEcdkqeyCqgitJqukPWRndsS6RSPGBmlU9liyoE,664
sendgrid/helpers/inbound/parse.py,sha256=3FkXWH5JvIAvh_8v_kRQmfureK6UJwjRIyZM8XXNkyo,3267
sendgrid/helpers/inbound/sample_data/default_data.txt,sha256=oyqpbnyyNm57yNdg3Pcf9jw70JPrs5GUqh17XHrkuZQ,1312
sendgrid/helpers/inbound/sample_data/default_data_with_attachments.txt,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sendgrid/helpers/inbound/sample_data/raw_data.txt,sha256=t2KBdaZUD-EaSOn2wZvFdITk06vByxneXXpMnKEP03s,1346
sendgrid/helpers/inbound/sample_data/raw_data_with_attachments.txt,sha256=mg5DYDdpehtIcgm1sEeP8U-6OjH8qETxixasDqNEpIo,15640
sendgrid/helpers/inbound/send.py,sha256=WGuw4r2s_1R_6OZzVo290enkAE1tXcyjbyzyuiVSxio,1772
sendgrid/helpers/mail/__init__.py,sha256=h8cfSU86TjWGZFaC7SE_400sR11yxTQF_CrUWOtgP-k,2474
sendgrid/helpers/mail/__pycache__/__init__.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/amp_html_content.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/asm.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/attachment.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/batch_id.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/bcc_email.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/bcc_settings.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/bcc_settings_email.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/bypass_bounce_management.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/bypass_list_management.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/bypass_spam_management.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/bypass_unsubscribe_management.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/category.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/cc_email.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/click_tracking.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/content.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/content_id.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/custom_arg.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/disposition.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/dynamic_template_data.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/email.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/exceptions.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/file_content.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/file_name.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/file_type.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/footer_html.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/footer_settings.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/footer_text.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/from_email.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/ganalytics.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/group_id.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/groups_to_display.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/header.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/html_content.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/ip_pool_name.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/mail.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/mail_settings.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/mime_type.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/open_tracking.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/open_tracking_substitution_tag.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/personalization.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/plain_text_content.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/reply_to.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/sandbox_mode.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/section.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/send_at.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/spam_check.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/spam_threshold.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/spam_url.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/subject.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/subscription_html.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/subscription_substitution_tag.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/subscription_text.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/subscription_tracking.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/substitution.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/template_id.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/to_email.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/tracking_settings.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/utm_campaign.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/utm_content.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/utm_medium.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/utm_source.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/utm_term.cpython-313.pyc,,
sendgrid/helpers/mail/__pycache__/validators.cpython-313.pyc,,
sendgrid/helpers/mail/amp_html_content.py,sha256=EWOypWCkMZTFeJHoBuymeW5FEIKwDyI8df8eK16QprA,1470
sendgrid/helpers/mail/asm.py,sha256=3IFpqVUY0LTKYjqbl8nkKYrdp6540VYSzP7C9n0B9YU,2525
sendgrid/helpers/mail/attachment.py,sha256=yTEgGzUZ0OdusQwQQLmuLjdxSCsUfvfSO9pzan8BH1s,7931
sendgrid/helpers/mail/batch_id.py,sha256=q9-KgQpgWhS0tPhdXByAfe5DZPmD7yblTImya0IiSjc,1309
sendgrid/helpers/mail/bcc_email.py,sha256=P9V3YY9nbrWeCL2HN_NlG2stq6Ie4mmj8pfNNB5Kp0M,98
sendgrid/helpers/mail/bcc_settings.py,sha256=IxYMv2-LVDBUWdhUCJK70UvXOHSFqtVTR25656hAbHM,1929
sendgrid/helpers/mail/bcc_settings_email.py,sha256=4YXdSZkkcbn9JBoP3a9k8IoXXUBkFsewqB5g93Mhbxw,1249
sendgrid/helpers/mail/bypass_bounce_management.py,sha256=vKVUW7UriH9bGFLc9cy42N5KktKd-oARd8mHCW8Hk08,1455
sendgrid/helpers/mail/bypass_list_management.py,sha256=FXWic4XcZsQCVWCxgnXwoox3RA-shayNasyATjC8494,1401
sendgrid/helpers/mail/bypass_spam_management.py,sha256=lsF871y7u8OqoMYwUF07nqkGuRt1QboXHda6kBWd7OQ,1436
sendgrid/helpers/mail/bypass_unsubscribe_management.py,sha256=PaY8J1S_OoqswJz0a1kWeUYe04X-DJHSxvpCE3TtobA,1599
sendgrid/helpers/mail/category.py,sha256=bInZxDPQQ3TaEUBQ2ZU9052NP9dEePDN-Uob0kJfcak,992
sendgrid/helpers/mail/cc_email.py,sha256=3D6S-FdBtEYatOeFeAX0EkZZiy98-h6yk7X-vF2Zhnw,96
sendgrid/helpers/mail/click_tracking.py,sha256=92o9UfF1s1t3rcXP1-5yR87b57xSXHpgYc09s_uavyM,2079
sendgrid/helpers/mail/content.py,sha256=tZshYZ2aS7BrrrGm_035hCMlAjo1jGBmKpfIYJ-ebXI,2313
sendgrid/helpers/mail/content_id.py,sha256=COtl3YnQ-uNvG9VEQvD0CDhTQXMKrWI1F9-a14jzisc,1718
sendgrid/helpers/mail/custom_arg.py,sha256=Q1CSmM2JBzOz4eAAiWGshCIUiEJcadgyloB6LihsUdU,2682
sendgrid/helpers/mail/disposition.py,sha256=Qa-pQDRk4y1ImP3h48ADbL_qdovS5Vomvxrki0IywmY,3123
sendgrid/helpers/mail/dynamic_template_data.py,sha256=Dct_TLCW7ej3AhHYbRIbhXONiltCMw6MJSUrQK9ApS4,2324
sendgrid/helpers/mail/email.py,sha256=IpSGsM6dGVVy9Q127bUGQUnQ_v11UjY00ziX2o7dF6g,7224
sendgrid/helpers/mail/exceptions.py,sha256=zU2mn_4k99DgdHPxVluoMwa7ZVrYcmg_neB8fkPWTh8,1831
sendgrid/helpers/mail/file_content.py,sha256=Bc9AbnvThA-EmgQIUP8sTlNzTZXY1WWihss-h-47aZE,1077
sendgrid/helpers/mail/file_name.py,sha256=KyK7C-ongiaH77vPoKYH0SaAn4imhsLdf6_AJ675Xc0,960
sendgrid/helpers/mail/file_type.py,sha256=CLdbVKzykAcHJpu1GxHwDt1v2yt3ktFZ_PaB6AaFr3k,1071
sendgrid/helpers/mail/footer_html.py,sha256=qS2TvBMY4xWm1Bh7G-Waw-h3ZO2uKDf1qQZtlnHZuJ0,982
sendgrid/helpers/mail/footer_settings.py,sha256=gu3wJ6JthQdqaq4bOfrlwD2aIekZljmAR0sVp08_yX8,2390
sendgrid/helpers/mail/footer_text.py,sha256=7nX6KVuwRtNDydHD5WaBKf_ST_sU5QXpgJP4HDBgGus,1011
sendgrid/helpers/mail/from_email.py,sha256=pGML0UvDW-g69yPXXoOVo5spDFmL2RVpGNjIfvdfzpg,100
sendgrid/helpers/mail/ganalytics.py,sha256=SK9PK2qWGYyWGTlKDF5ttwVDAemf2iSTnZPSoF6WBzE,5062
sendgrid/helpers/mail/group_id.py,sha256=ai8eQJyOgCIWmnc2yELjYxJWsmsIpf5EdBWpCXYKrKk,1048
sendgrid/helpers/mail/groups_to_display.py,sha256=xbZw8i6zEO7BuqfkA39WEylgo_vjpWk26mNNebt4Ku8,1783
sendgrid/helpers/mail/header.py,sha256=GiLEUsWmslX6Rf_NmiV2ulmmwWUWK0-A2SqYtZBlEyA,2640
sendgrid/helpers/mail/html_content.py,sha256=f6k0IQ5QR-F_1gA6hszfG7O9ErtgZmo7MnFOwc9msak,1434
sendgrid/helpers/mail/ip_pool_name.py,sha256=0i3ekMY6NcR5DnFi7IpBpD8c8-LypOxcr4LC7B_B6Cg,1159
sendgrid/helpers/mail/mail.py,sha256=_9jHBzUW7x-S26MCe4WXXl1KsxdGlDHwWh0SORSTok0,35486
sendgrid/helpers/mail/mail_settings.py,sha256=hInYN6d145F4zPZM8gYhD-8b49PmWHqkLZ6oy8L5dp0,8604
sendgrid/helpers/mail/mime_type.py,sha256=TZ6im9AYfAWw0rmeOVhzyxldUSRmgejHLApXEqi4_2k,167
sendgrid/helpers/mail/open_tracking.py,sha256=m-VYDLomeCfPfhTsawe11zXvMV7dLHXUkUS7vHnbYoY,2616
sendgrid/helpers/mail/open_tracking_substitution_tag.py,sha256=0AWoh5R-UbDM1tJVq2G7u7Hcb1RamHK5Q4ZqIjS2rIU,1968
sendgrid/helpers/mail/personalization.py,sha256=OxRaXpPMrZEKS1-uMkRBE8IoagNcdSeO2PKoIfvNLsc,7553
sendgrid/helpers/mail/plain_text_content.py,sha256=jbLqEL8P4YZjiGY2z_9jbih7OEN6USRYFuhrsbOcgDg,1455
sendgrid/helpers/mail/reply_to.py,sha256=_jKDhWsU38S5Vkc8LtplCOJPU7V0qDZhaBMOZiEqx6o,107
sendgrid/helpers/mail/sandbox_mode.py,sha256=o3vcYQMkJe3Kwj-R6bHQW5m3a2ZbeWW18aoeyE3V0Gw,1187
sendgrid/helpers/mail/section.py,sha256=z9BRa8V9mqEBo4BHvzwjNWR3NcoRL6NSs-2C7plyjGA,1506
sendgrid/helpers/mail/send_at.py,sha256=CJMgyHXxl2kkt1aXINI5U_9KAXFTFNeyzB_Ot3K4d48,2440
sendgrid/helpers/mail/spam_check.py,sha256=IZhIVg4g4aoxlnKKmcVVnTfL3JGf-s4I6f9sQZlsejc,3614
sendgrid/helpers/mail/spam_threshold.py,sha256=8egEwsBOjzq4nKrxBesYEpl0C3zbD0IJINvByG4wkGE,1879
sendgrid/helpers/mail/spam_url.py,sha256=CQlfH8kF7XG9chcbKqrl3yt5Rf8h9G0Xk7dXxOEzamI,1377
sendgrid/helpers/mail/subject.py,sha256=KSfloOpiF2Nke_hPFDbNklIdMTopMtBJNZpHI6EpFaI,1806
sendgrid/helpers/mail/subscription_html.py,sha256=tUpOXGEZ4oz6nb3oOn-udpGruodSSkkGjqQQyMiTxF0,1617
sendgrid/helpers/mail/subscription_substitution_tag.py,sha256=VI_nKeO1Mi6pWif_F3oVe6oCHWnqhv4Nh84hrw7Hnj8,2804
sendgrid/helpers/mail/subscription_text.py,sha256=x75n0Q9jrfCUKncTYQDUBaEBrZYKtz7owNItBrjikac,1617
sendgrid/helpers/mail/subscription_tracking.py,sha256=Flc5nIMUznNYjJV666WjRre5LNgah2oX6w7F_NAc5Cc,5176
sendgrid/helpers/mail/substitution.py,sha256=WingzYtAO3M-sVEecQa5mja6CVLMyfkDj9RKRCQkWAk,2548
sendgrid/helpers/mail/template_id.py,sha256=39vjDIYM4wM7pkvuLwIDvtJHsdqA57Qcq_T4N5y0sms,1002
sendgrid/helpers/mail/to_email.py,sha256=0S8v_wJ22sVIWAP9ns0QyylWaGpWNxxkJ9aGk27dNt0,96
sendgrid/helpers/mail/tracking_settings.py,sha256=YoL5VA_nNwc1GBZfYcng6VTYv3iPjz90XA82JZBOMQU,4790
sendgrid/helpers/mail/utm_campaign.py,sha256=HNPaIkfzD2gmnHMABmPUqeZhHet21H5U8pAhEw1P-60,992
sendgrid/helpers/mail/utm_content.py,sha256=dWVGZ1uZEBDf65w0g9cSd3imDbOczLPa2RvSPYkkDtg,1102
sendgrid/helpers/mail/utm_medium.py,sha256=WIilJlgokp_DPQzB-O445qg_2WCPNCa8xivP9qTWvkY,1028
sendgrid/helpers/mail/utm_source.py,sha256=-gwIsEj6SpzaemtKdnd12bBmUeOxGr_Y7khf_8bGyFQ,1213
sendgrid/helpers/mail/utm_term.py,sha256=m9I_YeZFC470dcVzXzkEvyUO1c_MwW1NDdKlF6hRrwQ,964
sendgrid/helpers/mail/validators.py,sha256=yX5Y_AnD5vuxXKVDP4sUiy49sSlxm73PdAU8EDHMqsY,2652
sendgrid/helpers/stats/__init__.py,sha256=Lcqu5TfRtG2Ku4g7rYEVyeR9UcaoliDfGIgS8jHQUGo,29
sendgrid/helpers/stats/__pycache__/__init__.cpython-313.pyc,,
sendgrid/helpers/stats/__pycache__/stats.cpython-313.pyc,,
sendgrid/helpers/stats/stats.py,sha256=6fXOcbzCMusVhF8VOeT6Y4t7VG4T1xpiLOujpuCmlAE,11031
sendgrid/sendgrid.py,sha256=EK0Ckr72z3IVUd5DG7klYbm156WxIFOkL4pNHZMJQ-0,2264
sendgrid/twilio_email.py,sha256=dyDU8sZYwsLhZmEpFuappMv9QJdR8EGiEoXFrY0m6ZE,3081
sendgrid/version.py,sha256=7CpHS2y8heNng3d0yLKZ6NhMyNhWRqQv1Jcxcvbm1fk,23
test/integ/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
test/integ/__pycache__/__init__.cpython-313.pyc,,
test/integ/__pycache__/test_sendgrid.cpython-313.pyc,,
test/integ/test_sendgrid.py,sha256=IA8L1ONKPEngA27Ns2_CzdH1xN9FWGjwDcC-DBW8dBI,87414
test/unit/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
test/unit/__pycache__/__init__.cpython-313.pyc,,
test/unit/__pycache__/test_app.cpython-313.pyc,,
test/unit/__pycache__/test_config.cpython-313.pyc,,
test/unit/__pycache__/test_email.cpython-313.pyc,,
test/unit/__pycache__/test_eventwebhook.cpython-313.pyc,,
test/unit/__pycache__/test_inbound_send.cpython-313.pyc,,
test/unit/__pycache__/test_mail_helpers.cpython-313.pyc,,
test/unit/__pycache__/test_parse.cpython-313.pyc,,
test/unit/__pycache__/test_project.cpython-313.pyc,,
test/unit/__pycache__/test_spam_check.cpython-313.pyc,,
test/unit/__pycache__/test_stats.cpython-313.pyc,,
test/unit/__pycache__/test_twilio_email.cpython-313.pyc,,
test/unit/__pycache__/test_unassigned.cpython-313.pyc,,
test/unit/test_app.py,sha256=OuhklHZ1X9pwX2J9j6GKqboIPBSrJLTewtqMyUKZ6I8,602
test/unit/test_config.py,sha256=O9XIk2hNLgZ6ZLmvQI98A0lq0jebJSmmgE1VlyMmIJM,1925
test/unit/test_email.py,sha256=5E77RqhfVHSfWIrwE_PPdbG58UybLNTYS95psfkmqsc,1901
test/unit/test_eventwebhook.py,sha256=3ctp_yikqT1ZMBE0d3bfdsnmfL3nWSvCvEAnsZUnCUc,2316
test/unit/test_inbound_send.py,sha256=j2A1UAC6Oqdl2GMnGLvo1xDLvmWYjfOZL1KQl_iF74c,1454
test/unit/test_mail_helpers.py,sha256=uOvjhPXXoUeTRiI1cFrB9sY0lRg1Y_04wOffUwTY_I8,71395
test/unit/test_parse.py,sha256=OUdJvzWoPGVcf13G45wWEdnVMXHQRKvcwsljfxRyouY,453
test/unit/test_project.py,sha256=j7pmCkTIZGgbycOw_nIo9DobnLELY2P-FfgstYutQpk,1367
test/unit/test_spam_check.py,sha256=i91g_JsCBLBXg6ItBLGK1yMeJ1CkDYOQd5l8ccrxeuk,1583
test/unit/test_stats.py,sha256=C_YNx8hlyk593pB7hKPDEafLHuMSBeBnnmQwFUxC4rI,2847
test/unit/test_twilio_email.py,sha256=jNv6NB_gKMOMMxL9eUz6InywzTaY0rDv7eCGB3RLnG8,1224
test/unit/test_unassigned.py,sha256=l5w1laNDvkRGe8TYCiSV976hJ5DjdYWJJ5F5oFTJYog,1945
