"use client";

import { motion, useInView } from "framer-motion";
import Link from "next/link";
import { useRef, useState } from "react";
import {
  FaRocket,
  FaShareAlt,
  FaBuilding,
  FaComments,
  FaStar
} from "react-icons/fa";

import { FOOTER_DATA } from "@/constants";
import { GlowingEffect } from "@/components/ui/glowing-effect";
import FlowingMenu from "@/components/ui/FlowingMenu";
import { staggerContainer, fadeIn, slideInFromLeft, slideInFromRight } from "@/lib/motion";

export const Footer = () => {
  const footerRef = useRef(null);
  const isInView = useInView(footerRef, { once: true, amount: 0.1 });
  const [hoveredColumn, setHoveredColumn] = useState<number | null>(null);

  return (
    <footer
      ref={footerRef}
      className="relative w-full bg-transparent text-gray-200 mt-20 overflow-hidden"
    >
      {/* Background gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-t from-[rgba(3,0,20,0.95)] via-[rgba(15,5,40,0.8)] to-transparent pointer-events-none" />

      {/* Main footer content */}
      <motion.div
        variants={staggerContainer(0.1, 0.2)}
        initial="hidden"
        animate={isInView ? "visible" : "hidden"}
        className="relative z-10 max-w-7xl mx-auto px-6 py-16"
      >
        {/* Top section with logo and company info */}
        <motion.div
          variants={fadeIn(0, 0.8)}
          className="text-center mb-16"
        >
          <div className="flex flex-col items-center space-y-6">
            {/* Company Logo/Name */}
            <motion.div
              whileHover={{ scale: 1.05 }}
              transition={{ type: "spring", stiffness: 300, damping: 20 }}
              className="relative"
            >
              <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-purple-400 via-cyan-400 to-purple-600 bg-clip-text text-transparent">
                Oyu Intelligence
              </h2>
              <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 to-cyan-600 rounded-lg blur opacity-20 group-hover:opacity-40 transition duration-1000"></div>
            </motion.div>

            {/* Company tagline */}
            <motion.p
              variants={fadeIn(0.2, 0.8)}
              className="text-lg text-gray-300 max-w-2xl leading-relaxed"
            >
              Transforming businesses through innovative AI solutions, cutting-edge mobile applications,
              and premium digital experiences.
            </motion.p>
          </div>
        </motion.div>

        {/* Footer columns with creative designs */}
        <motion.div
          variants={staggerContainer(0.1, 0.4)}
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8 mb-8 sm:mb-10 lg:mb-12"
        >
          {FOOTER_DATA.map((column, columnIndex) => {
            // Define unique styling for each column
            const getColumnStyle = (index: number, title: string) => {
              switch (title) {
                case "Services":
                  return {
                    gradient: "from-[rgba(147,51,234,0.3)] via-[rgba(79,70,229,0.2)] to-[rgba(30,10,60,0.4)]",
                    icon: FaRocket,
                    iconGradient: "from-purple-500 to-indigo-600",
                    accentColor: "text-purple-400",
                    hoverColor: "group-hover/link:text-purple-300"
                  };
                case "Company":
                  return {
                    gradient: "from-[rgba(6,182,212,0.3)] via-[rgba(14,165,233,0.2)] to-[rgba(30,10,60,0.4)]",
                    icon: FaBuilding,
                    iconGradient: "from-cyan-500 to-blue-600",
                    accentColor: "text-cyan-400",
                    hoverColor: "group-hover/link:text-cyan-300"
                  };
                case "Social Media":
                  return {
                    gradient: "from-[rgba(236,72,153,0.3)] via-[rgba(168,85,247,0.2)] to-[rgba(30,10,60,0.4)]",
                    icon: FaShareAlt,
                    iconGradient: "from-pink-500 to-purple-600",
                    accentColor: "text-pink-400",
                    hoverColor: "group-hover/link:text-pink-300"
                  };
                case "Contact":
                  return {
                    gradient: "from-[rgba(34,197,94,0.3)] via-[rgba(16,185,129,0.2)] to-[rgba(30,10,60,0.4)]",
                    icon: FaComments,
                    iconGradient: "from-green-500 to-emerald-600",
                    accentColor: "text-green-400",
                    hoverColor: "group-hover/link:text-green-300"
                  };
                default:
                  return {
                    gradient: "from-[rgba(30,10,60,0.4)] via-[rgba(15,5,40,0.5)] to-[rgba(3,0,20,0.6)]",
                    icon: FaStar,
                    iconGradient: "from-purple-500 to-cyan-600",
                    accentColor: "text-purple-400",
                    hoverColor: "group-hover/link:text-cyan-400"
                  };
              }
            };

            const style = getColumnStyle(columnIndex, column.title);
            const IconComponent = style.icon;

            return (
              <motion.div
                key={column.title}
                variants={fadeIn(columnIndex * 0.1, 0.6)}
                onMouseEnter={() => setHoveredColumn(columnIndex)}
                onMouseLeave={() => setHoveredColumn(null)}
                className="relative group h-full"
              >
                {/* Enhanced column container */}
                <div className={`relative p-6 rounded-2xl backdrop-blur-xl bg-gradient-to-br ${style.gradient} border border-[#7042F88B] hover:border-purple-500/50 transition-all duration-500 overflow-hidden h-full min-h-[280px]`}>
                  <GlowingEffect
                    spread={100}
                    glow={hoveredColumn === columnIndex}
                    disabled={hoveredColumn !== columnIndex}
                    proximity={130}
                    inactiveZone={0.05}
                    blur={4}
                    borderWidth={3}
                  />

                  {/* Decorative background icon */}
                  <div className="absolute top-4 right-4 opacity-10 group-hover:opacity-20 transition-opacity duration-500">
                    <IconComponent className="w-16 h-16 text-white" />
                  </div>

                  {/* Column header with icon */}
                  <div className="relative z-10 flex items-center mb-6">
                    <motion.div
                      className={`w-10 h-10 rounded-xl bg-gradient-to-br ${style.iconGradient} flex items-center justify-center mr-3 group-hover:scale-110 transition-transform duration-300`}
                      whileHover={{ rotate: 10 }}
                    >
                      <IconComponent className="w-5 h-5 text-white" />
                    </motion.div>
                    <motion.h3
                      whileHover={{ scale: 1.02 }}
                      className="font-bold text-xl text-white"
                    >
                      {column.title}
                    </motion.h3>
                  </div>

                  {/* Column links with enhanced styling */}
                  <div className="space-y-2 relative z-10">
                    {column.data.map(({ icon: Icon, name, link }, linkIndex) => (
                      <motion.div
                        key={`${column.title}-${name}`}
                        initial={{ opacity: 0, x: -20 }}
                        animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -20 }}
                        transition={{ delay: (columnIndex * 0.1) + (linkIndex * 0.05), duration: 0.5 }}
                      >
                        <Link
                          href={link}
                          target={link.startsWith('http') ? "_blank" : "_self"}
                          rel={link.startsWith('http') ? "noreferrer noopener" : undefined}
                          className="group/link flex items-center space-x-3 text-gray-300 hover:text-white transition-all duration-300 p-3 rounded-xl hover:bg-white/10 hover:backdrop-blur-sm"
                        >
                          {Icon ? (
                            <motion.div
                              whileHover={{ scale: 1.3, rotate: 8 }}
                              transition={{ type: "spring", stiffness: 400, damping: 15 }}
                              className="flex-shrink-0"
                            >
                              <Icon className={`w-4 h-4 ${style.accentColor} ${style.hoverColor} transition-colors duration-300`} />
                            </motion.div>
                          ) : (
                            <motion.div
                              whileHover={{ scale: 1.2 }}
                              className={`w-2 h-2 rounded-full bg-gradient-to-r ${style.iconGradient} flex-shrink-0`}
                            />
                          )}
                          <span className="text-sm font-medium group-hover/link:translate-x-1 transition-transform duration-300">
                            {name}
                          </span>
                          <motion.div
                            className="ml-auto opacity-0 group-hover/link:opacity-100 transition-opacity duration-300"
                            whileHover={{ x: 3 }}
                          >
                            <svg className="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                            </svg>
                          </motion.div>
                        </Link>
                      </motion.div>
                    ))}
                  </div>

                  {/* Bottom accent line */}
                  <motion.div
                    className={`absolute bottom-0 left-0 h-1 bg-gradient-to-r ${style.iconGradient} transition-all duration-500`}
                    initial={{ width: "0%" }}
                    animate={{ width: hoveredColumn === columnIndex ? "100%" : "30%" }}
                  />
                </div>
              </motion.div>
            );
          })}
        </motion.div>

        {/* Flowing Menu Section */}
        <motion.div
          variants={fadeIn(1.0, 0.8)}
          className="mb-12"
        >
          <div className="text-center mb-8">
            <motion.h3
              variants={fadeIn(1.2, 0.6)}
              className="text-3xl font-bold text-white mb-4"
            >
              Explore Our <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-cyan-400">Universe</span>
            </motion.h3>
            <motion.p
              variants={fadeIn(1.4, 0.6)}
              className="text-gray-300 max-w-2xl mx-auto"
            >
              Discover our innovative solutions and cutting-edge technologies that transform businesses
            </motion.p>
          </div>

          <motion.div
            variants={fadeIn(1.6, 0.8)}
            className="relative h-[400px] rounded-2xl overflow-hidden"
          >
            <FlowingMenu items={[
              {
                link: '#services',
                text: 'AI Automation',
                image: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=600&h=400&fit=crop&crop=center'
              },
              {
                link: '#services-detail',
                text: 'Mobile Development',
                image: 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=600&h=400&fit=crop&crop=center'
              },
              {
                link: '#services-detail',
                text: 'Web Solutions',
                image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=600&h=400&fit=crop&crop=center'
              },
              {
                link: '#portfolio',
                text: 'Our Portfolio',
                image: 'https://images.unsplash.com/photo-1551650975-87deedd944c3?w=600&h=400&fit=crop&crop=center'
              }
            ]} />
          </motion.div>
        </motion.div>

        {/* Bottom section with copyright and additional links */}
        <motion.div
          variants={fadeIn(1.0, 0.8)}
          className="border-t border-white/10 pt-8"
        >
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            {/* Copyright */}
            <motion.div
              variants={slideInFromLeft(1.2)}
              className="text-center md:text-left"
            >
              <p className="text-gray-400 text-sm">
                &copy; {new Date().getFullYear()} Oyu Intelligence LLC. All rights reserved.
              </p>
              <p className="text-gray-500 text-xs mt-1">
                Crafted with ❤️ in Mongolia
              </p>
            </motion.div>

            {/* Additional links */}
            <motion.div
              variants={slideInFromRight(1.2)}
              className="flex space-x-6 text-sm"
            >
              <Link
                href="/privacy"
                className="text-gray-400 hover:text-white transition-colors duration-300"
              >
                Privacy Policy
              </Link>
              <Link
                href="/terms"
                className="text-gray-400 hover:text-white transition-colors duration-300"
              >
                Terms of Service
              </Link>
              <Link
                href="/sitemap"
                className="text-gray-400 hover:text-white transition-colors duration-300"
              >
                Sitemap
              </Link>
            </motion.div>
          </div>
        </motion.div>
      </motion.div>

      {/* Decorative elements */}
      <div className="absolute top-0 left-1/4 w-64 h-64 bg-purple-500/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 right-1/4 w-64 h-64 bg-cyan-500/10 rounded-full blur-3xl"></div>
    </footer>
  );
};
