@echo off
echo 🚀 Starting Oyu Intelligence Development Environment
echo ==================================================

REM Check if we're in the right directory
if not exist "package.json" (
    echo ❌ Error: Please run this script from the project root directory
    pause
    exit /b 1
)

echo.
echo 📧 Starting Python Email Service...
start "Python Email Service" cmd /k "cd api && pip install -r requirements.txt && python email_service.py"

echo.
echo ⚛️ Starting Next.js Development Server...
start "Next.js Dev Server" cmd /k "npm install && npm run dev"

echo.
echo 🎉 Development environment is starting!
echo ==================================================
echo 📱 Website: http://localhost:3000
echo 📧 Email API: http://localhost:5000
echo 🧪 Test Page: http://localhost:3000/test-contact
echo ==================================================
echo.
echo Services are starting in separate windows.
echo Close the terminal windows to stop the services.
echo.
pause
