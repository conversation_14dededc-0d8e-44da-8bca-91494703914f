'use client';
import { useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";

import { NAV_LINKS, SOCIALS } from "@/constants";
import { NavLinkType } from "@/types";

export const Navbar = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const pathname = usePathname();
  const isOnHomePage = pathname === '/';

  // Helper function to determine link class
  const getLinkClass = (link: NavLinkType, isCenter = false): string => {
    // On home page, all links should be white
    if (isOnHomePage) {
      return `cursor-pointer transition ${isCenter ? 'text-center' : 'mx-1'} text-white hover:text-[rgb(112,66,248)]`;
    }

    // On other pages, determine active state
    const isActive =
      pathname === link.link ||
      (pathname.includes(link.link) && !link.link.startsWith('#'));

    return `cursor-pointer transition ${isCenter ? 'text-center' : 'mx-1'} ${
      isActive ? 'text-[rgb(112,66,248)]' : 'text-white hover:text-[rgb(112,66,248)]'
    }`;
  };

  return (
    <div className="w-full h-[60px] sm:h-[65px] fixed top-0 shadow-lg shadow-[#2A0E61]/50 bg-[#03001427] backdrop-blur-md z-50 px-3 sm:px-4 md:px-6 lg:px-10">
      {/* Navbar Container */}
      <div className="w-full h-full flex items-center justify-between m-auto">
        {/* Logo + Name */}
        <Link
          href="/"
          className="flex items-center"
        >
          <Image
            src="/logo.png"
            alt="Oyu Intelligence Logo"
            width={60}
            height={60}
            draggable={false}
            className="cursor-pointer w-[40px] h-[40px] sm:w-[50px] sm:h-[50px] md:w-[60px] md:h-[60px] lg:w-[70px] lg:h-[70px]"
            priority
          />
          <div className="hidden md:flex font-bold ml-[8px] lg:ml-[10px] text-white text-sm lg:text-base">Oyu Intelligence LLC</div>
        </Link>

        {/* Web Navbar */}
        <div className="hidden md:flex w-full max-w-[600px] h-full flex-row items-center justify-between md:mr-10 lg:mr-20">
          <div className="flex items-center justify-between w-full h-auto border-[rgba(112,66,248,0.38)] bg-[rgba(3,0,20,0.37)] mr-[15px] px-[15px] lg:px-[25px] py-[10px] rounded-full">
            {NAV_LINKS.map((link) => (
              <Link
                key={link.title}
                href={link.link.startsWith('#') && !isOnHomePage ? `/${link.link}` : link.link}
                className={getLinkClass(link)}
              >
                {link.title}
              </Link>
            ))}
          </div>
        </div>

        {/* Social Icons (Web) */}
        <div className="hidden md:flex flex-row gap-3 lg:gap-5">
          {SOCIALS.map(({ link, name, icon: Icon }) => (
            <Link
              href={link}
              target="_blank"
              rel="noreferrer noopener"
              key={name}
            >
              <Icon className="h-5 w-5 lg:h-6 lg:w-6 text-white" />
            </Link>
          ))}
        </div>

        {/* Hamburger Menu */}
        <button
          className="md:hidden text-white focus:outline-none text-2xl sm:text-3xl p-1"
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          aria-label="Toggle menu"
        >
          {isMobileMenuOpen ? "✕" : "☰"}
        </button>
      </div>

      {/* Mobile Menu */}
      {isMobileMenuOpen && (
        <div className="absolute top-[60px] sm:top-[65px] left-0 w-full bg-[#030014]/95 backdrop-blur-md p-4 sm:p-5 flex flex-col items-center md:hidden border-t border-[#2A0E61]/50 z-50">
          {/* Links */}
          <div className="flex flex-col items-center gap-4 sm:gap-5 w-full">
            {NAV_LINKS.map((link) => (
              <Link
                key={link.title}
                href={link.link.startsWith('#') && !isOnHomePage ? `/${link.link}` : link.link}
                className={`${getLinkClass(link, true)} text-base sm:text-lg py-2 w-full text-center`}
                onClick={() => setIsMobileMenuOpen(false)}
              >
                {link.title}
              </Link>
            ))}
          </div>

          {/* Social Icons */}
          <div className="flex justify-center gap-6 sm:gap-8 mt-6 sm:mt-8 w-full border-t border-[#2A0E61]/50 pt-4 sm:pt-5">
            {SOCIALS.map(({ link, name, icon: Icon }) => (
              <Link
                href={link}
                target="_blank"
                rel="noreferrer noopener"
                key={name}
                className="p-2"
              >
                <Icon className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </Link>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};