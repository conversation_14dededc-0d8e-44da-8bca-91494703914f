import type { Config } from 'tailwindcss'

const config: Config = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      screens: {
        'xs': '475px',
        '3xl': '1600px',
      },
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'gradient-conic':
          'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
      },
      typography: {
        DEFAULT: {
          css: {
            color: '#e2e8f0', // text-gray-200
            a: {
              color: '#a78bfa', // text-purple-400
              '&:hover': {
                color: '#c4b5fd', // text-purple-300
              },
            },
            h1: {
              color: '#f8fafc', // text-gray-50
            },
            h2: {
              color: '#f8fafc', // text-gray-50
            },
            h3: {
              color: '#f8fafc', // text-gray-50
            },
            h4: {
              color: '#f8fafc', // text-gray-50
            },
            h5: {
              color: '#f8fafc', // text-gray-50
            },
            h6: {
              color: '#f8fafc', // text-gray-50
            },
            strong: {
              color: '#f8fafc', // text-gray-50
            },
            code: {
              color: '#f8fafc', // text-gray-50
              backgroundColor: 'rgba(30, 10, 60, 0.5)',
              padding: '0.2em 0.4em',
              borderRadius: '0.25rem',
              fontWeight: '500',
            },
            blockquote: {
              color: '#cbd5e1', // text-gray-300
              borderLeftColor: '#7c3aed', // border-purple-600
              backgroundColor: 'rgba(30, 10, 60, 0.2)',
              padding: '1rem',
              borderRadius: '0.25rem',
            },
            ul: {
              li: {
                '&::marker': {
                  color: '#a78bfa', // text-purple-400
                },
              },
            },
            ol: {
              li: {
                '&::marker': {
                  color: '#a78bfa', // text-purple-400
                },
              },
            },
            hr: {
              borderColor: '#4c1d95', // border-purple-900
            },
            pre: {
              backgroundColor: 'rgba(15, 5, 30, 0.8)',
              color: '#f8fafc', // text-gray-50
              borderRadius: '0.5rem',
              border: '1px solid rgba(124, 58, 237, 0.3)', // border-purple-600/30
            },
            table: {
              thead: {
                borderBottomColor: '#4c1d95', // border-purple-900
                th: {
                  color: '#f8fafc', // text-gray-50
                },
              },
              tbody: {
                tr: {
                  borderBottomColor: '#4c1d95', // border-purple-900
                },
                td: {
                  color: '#e2e8f0', // text-gray-200
                },
              },
            },
          },
        },
      },
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
  ],
}
export default config
