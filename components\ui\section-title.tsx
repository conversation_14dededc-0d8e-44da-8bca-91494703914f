"use client";

import { motion, useInView } from "framer-motion";
import { ReactNode, useRef } from "react";

interface SectionTitleProps {
  subtitle?: string;
  title: string;
  highlightWord?: string;
  description?: string;
  size?: "small" | "medium" | "large";
  alignment?: "left" | "center" | "right";
  icon?: ReactNode;
  className?: string;
}

export const SectionTitle = ({
  subtitle,
  title,
  highlightWord,
  description,
  size = "medium",
  alignment = "center",
  icon,
  className = ""
}: SectionTitleProps) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, amount: 0.3 });

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: {
      opacity: 0,
      y: 20
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  // Size configurations - standardized to match <PERSON>er's "Explore Our Universe" style
  const sizeConfig = {
    small: {
      subtitle: "text-xs sm:text-sm",
      title: "text-2xl sm:text-3xl",
      description: "text-sm sm:text-base",
      spacing: "space-y-2 sm:space-y-3"
    },
    medium: {
      subtitle: "text-sm sm:text-base",
      title: "text-3xl font-bold",
      description: "text-base sm:text-lg",
      spacing: "space-y-3 sm:space-y-4"
    },
    large: {
      subtitle: "text-base sm:text-lg",
      title: "text-3xl font-bold",
      description: "text-lg sm:text-xl",
      spacing: "space-y-4 sm:space-y-6"
    }
  };

  // Alignment classes
  const alignmentClasses = {
    left: "text-left items-start",
    center: "text-center items-center",
    right: "text-right items-end"
  };

  const config = sizeConfig[size];
  const alignClass = alignmentClasses[alignment];

  // Function to highlight specific word in title
  const renderTitle = () => {
    if (!highlightWord) {
      return (
        <span className="text-white font-bold">
          {title}
        </span>
      );
    }

    const parts = title.split(new RegExp(`(${highlightWord})`, 'gi'));
    return (
      <span className="font-bold">
        {parts.map((part, index) => (
          <span
            key={index}
            className={
              part.toLowerCase() === highlightWord.toLowerCase()
                ? "text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-cyan-400"
                : "text-white"
            }
          >
            {part}
          </span>
        ))}
      </span>
    );
  };

  return (
    <motion.div
      ref={ref}
      variants={containerVariants}
      initial="hidden"
      animate={isInView ? "visible" : "hidden"}
      className={`flex flex-col ${alignClass} ${config.spacing} ${className}`}
    >
      {/* Subtitle with icon */}
      {subtitle && (
        <motion.div
          variants={itemVariants}
          className="flex items-center gap-2 justify-center"
        >
          {icon && (
            <div className="flex items-center justify-center w-8 h-8 rounded-full bg-gradient-to-r from-purple-500/20 to-cyan-500/20 border border-purple-500/30 backdrop-blur-sm">
              <div className="text-purple-400">
                {icon}
              </div>
            </div>
          )}
          <span className={`${config.subtitle} text-purple-300 font-medium tracking-wide uppercase`}>
            {subtitle}
          </span>
        </motion.div>
      )}

      {/* Main title */}
      <motion.h2
        variants={itemVariants}
        className={`${config.title} leading-tight tracking-tight`}
      >
        {renderTitle()}
      </motion.h2>

      {/* Animated underline */}
      <motion.div
        variants={itemVariants}
        className="relative"
      >
        <div className="h-1 w-20 mx-auto bg-gradient-to-r from-purple-500 to-cyan-500 rounded-full opacity-80" />
        <div className="absolute inset-0 h-1 w-20 mx-auto bg-gradient-to-r from-purple-500 to-cyan-500 rounded-full blur-sm opacity-60" />
      </motion.div>

      {/* Description */}
      {description && (
        <motion.p
          variants={itemVariants}
          className={`${config.description} text-gray-300 leading-relaxed max-w-3xl ${alignment === 'center' ? 'mx-auto' : ''}`}
        >
          {description}
        </motion.p>
      )}
    </motion.div>
  );
};
