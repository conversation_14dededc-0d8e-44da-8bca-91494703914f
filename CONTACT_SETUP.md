# Contact Form Email Setup Guide

This guide explains how to set up and run the contact form email functionality for the Oyu Intelligence website.

## Overview

The contact form now uses a real email sending system with the following architecture:
- **Frontend:** Next.js contact form (React component)
- **Next.js API:** `/api/contact` route that validates and forwards requests
- **Python API:** Flask service that sends emails via SendGrid
- **Email Service:** SendGrid for reliable email delivery

## Quick Start

### Option 1: Automatic Setup (Recommended)

1. **Install Python dependencies:**
   ```bash
   npm run setup:email
   ```

2. **Start both services simultaneously:**
   ```bash
   npm run dev:full
   ```

This will start:
- Next.js development server on `http://localhost:3000`
- Python email service on `http://localhost:5000`

### Option 2: Manual Setup

1. **Start the Python email service:**
   ```bash
   # Windows
   cd api
   run_email_service.py
   
   # Linux/Mac
   cd api
   python run_email_service.py
   ```

2. **In a new terminal, start Next.js:**
   ```bash
   npm run dev
   ```

## Configuration

### SendGrid Setup (IMPORTANT)

**⚠️ CRITICAL STEP:** Before the email service can work, you must verify the sender email in SendGrid.

1. **Go to SendGrid Dashboard:**
   - Visit: https://app.sendgrid.com/
   - Log in with your SendGrid account

2. **Verify Sender Identity:**
   - Navigate to: Settings → Sender Authentication → Single Sender Verification
   - Click "Create New Sender"
   - Add `<EMAIL>` as the sender email
   - Fill in the required information:
     - From Name: Oyu Intelligence
     - From Email Address: <EMAIL>
     - Reply To: <EMAIL>
     - Company Address: Sambuu St 47, CHD - 5 khoroo, Ulaanbaatar 15171
   - Click "Create"
   - **Check your email** (<EMAIL>) for verification link
   - **Click the verification link** to verify the sender

3. **Test the Setup:**
   ```bash
   cd api
   python test_sendgrid.py
   ```

### SendGrid API Key
The SendGrid API key is configured via environment variables in `api/email_service.py`:
```python
SENDGRID_API_KEY = os.getenv('SENDGRID_API_KEY')
```
Set your actual API key in `.env.local` file.

### Email Addresses
- **From Email:** <EMAIL> (verified in SendGrid ✅)
- **To Email:** <EMAIL> (where contact form submissions are sent)

### Status
✅ **READY TO USE!** The sender email has been verified and real email sending is now enabled.

## Testing the Contact Form

1. **Open the website:** `http://localhost:3000`
2. **Navigate to the Contact section**
3. **Fill out the form with:**
   - Name: Your name
   - Email: A valid email address
   - Subject: Test message
   - Message: Your test message
4. **Click "Send Message"**

### Expected Behavior
- ✅ Form shows loading state while submitting
- ✅ Success message appears after successful submission
- ✅ Email is <NAME_EMAIL>
- ❌ Error message appears if submission fails

## API Endpoints

### Next.js API Route
- **URL:** `POST /api/contact`
- **Purpose:** Validates form data and forwards to Python API

### Python Email Service
- **Health Check:** `GET http://localhost:5000/health`
- **Send Email:** `POST http://localhost:5000/send-email`

## Troubleshooting

### Common Issues

1. **"Failed to send email" error:**
   - Check if Python email service is running on port 5000
   - Verify SendGrid API key is valid
   - Check network connectivity

2. **Port 5000 already in use:**
   - Stop other services using port 5000
   - Or modify the port in `api/email_service.py` and `.env.local`

3. **Python dependencies not installed:**
   ```bash
   cd api
   pip install -r requirements.txt
   ```

4. **CORS errors:**
   - The Python API includes CORS headers
   - Ensure both services are running on correct ports

### Testing API Directly

Test the Python email service directly:
```bash
curl -X POST http://localhost:5000/send-email \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test User",
    "email": "<EMAIL>",
    "subject": "Test Subject",
    "message": "This is a test message"
  }'
```

## Security Features

- ✅ Input validation and sanitization
- ✅ Email format validation
- ✅ Content length limits
- ✅ HTML/script tag removal
- ✅ CORS protection
- ✅ Error handling and logging

## Production Deployment

For production deployment:

1. **Environment Variables:**
   - Move SendGrid API key to environment variable
   - Update `PYTHON_API_URL` in `.env.local`

2. **Server Setup:**
   - Deploy Python API to a cloud service (Heroku, AWS, etc.)
   - Update the API URL in Next.js configuration

3. **Domain Configuration:**
   - Ensure SendGrid sender email is verified
   - Configure proper CORS origins

## File Structure

```
├── api/
│   ├── email_service.py          # Main Python Flask API
│   ├── requirements.txt          # Python dependencies
│   ├── run_email_service.py      # Startup script

│   └── README.md                 # API documentation
├── app/api/contact/
│   └── route.ts                  # Next.js API route
├── components/main/
│   └── contact.tsx               # Contact form component
├── .env.local                    # Environment variables
└── CONTACT_SETUP.md             # This file
```

## Support

If you encounter any issues:
1. Check the console logs in both browser and terminal
2. Verify all services are running
3. Test the API endpoints directly
4. Check SendGrid dashboard for email delivery status
