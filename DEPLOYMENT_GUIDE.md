# 🚀 Deployment Guide

## Overview

This guide covers deploying the Oyu Intelligence space portfolio to production environments.

## 🌐 Frontend Deployment (Vercel - Recommended)

### Prerequisites
- GitHub repository (already set up)
- Vercel account

### Steps

1. **Connect to Vercel**
   - Go to [vercel.com](https://vercel.com)
   - Sign in with GitHub
   - Click "New Project"
   - Import your repository: `boldbat/Oyu-Mind-Space`

2. **Configure Environment Variables**
   In Vercel dashboard, add these environment variables:
   ```
   PYTHON_API_URL=https://your-api-domain.com
   ```

3. **Deploy**
   - Vercel will automatically deploy on push to main branch
   - Your site will be available at: `https://your-project.vercel.app`

## 🐍 Backend Deployment (Python Email Service)

### Option 1: Heroku

1. **Install Heroku CLI**
2. **Create Heroku app**
   ```bash
   heroku create your-app-name
   ```

3. **Configure environment variables**
   ```bash
   heroku config:set SENDGRID_API_KEY=your_api_key
   heroku config:set FROM_EMAIL=<EMAIL>
   heroku config:set TO_EMAIL=<EMAIL>
   heroku config:set TEST_MODE=false
   ```

4. **Deploy**
   ```bash
   git subtree push --prefix api heroku main
   ```

### Option 2: Railway

1. **Connect GitHub repository**
2. **Select the `api/` folder**
3. **Add environment variables** in Railway dashboard
4. **Deploy automatically**

### Option 3: DigitalOcean App Platform

1. **Create new app**
2. **Connect GitHub repository**
3. **Configure Python service** pointing to `api/` folder
4. **Add environment variables**
5. **Deploy**

## 🔧 Environment Variables for Production

### Frontend (.env.local or Vercel)
```env
PYTHON_API_URL=https://your-api-domain.com
```

### Backend (Python Service)
```env
SENDGRID_API_KEY=your_production_sendgrid_key
FROM_EMAIL=<EMAIL>
TO_EMAIL=<EMAIL>
TEST_MODE=false
```

## 📧 SendGrid Production Setup

### 1. Domain Authentication
- Go to SendGrid → Settings → Sender Authentication
- Add your domain (e.g., `yourdomain.com`)
- Add DNS records provided by SendGrid
- Verify domain authentication

### 2. Sender Verification
- Verify `<EMAIL>` as sender
- Update environment variables with verified email

### 3. Email Templates
- The service uses professional HTML templates
- Monitor delivery rates in SendGrid dashboard

## 🔒 Security for Production

### 1. Environment Variables
- Never commit `.env.local` to git
- Use platform-specific environment variable systems
- Rotate API keys regularly

### 2. CORS Configuration
- Update CORS settings for production domains
- Restrict origins to your actual domains

### 3. Rate Limiting
Consider adding rate limiting to prevent spam:
```python
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address

limiter = Limiter(
    app,
    key_func=get_remote_address,
    default_limits=["200 per day", "50 per hour"]
)

@app.route('/send-email', methods=['POST'])
@limiter.limit("5 per minute")
def send_email():
    # existing code
```

## 📊 Monitoring

### 1. SendGrid Dashboard
- Monitor email delivery rates
- Check bounce and spam rates
- View email analytics

### 2. Application Logs
- Monitor Python service logs
- Check for errors and performance issues
- Set up alerts for failures

### 3. Uptime Monitoring
- Use services like UptimeRobot
- Monitor both frontend and backend
- Set up notifications for downtime

## 🧪 Testing Production

### 1. Smoke Tests
```bash
# Test frontend
curl https://your-site.vercel.app

# Test backend health
curl https://your-api-domain.com/health

# Test email sending
curl -X POST https://your-api-domain.com/send-email \
  -H "Content-Type: application/json" \
  -d '{"name":"Test","email":"<EMAIL>","subject":"Test","message":"Test"}'
```

### 2. Email Delivery Test
- Submit contact form on live site
- Verify email delivery
- Check spam folder if needed

## 🔄 CI/CD Pipeline

### Automatic Deployment
- Vercel: Deploys automatically on push to main
- Backend: Set up automatic deployment from GitHub

### Environment Promotion
1. **Development**: Local environment with test mode
2. **Staging**: Test environment with real SendGrid
3. **Production**: Live environment with monitoring

## 📝 Deployment Checklist

### Pre-Deployment
- [ ] Environment variables configured
- [ ] SendGrid domain authenticated
- [ ] Sender email verified
- [ ] DNS records updated
- [ ] Security review completed

### Post-Deployment
- [ ] Frontend accessible
- [ ] Backend health check passes
- [ ] Contact form sends emails
- [ ] Emails reach inbox (not spam)
- [ ] Monitoring set up
- [ ] Performance tested

### Rollback Plan
- [ ] Previous version tagged in git
- [ ] Rollback procedure documented
- [ ] Database backup (if applicable)
- [ ] DNS rollback plan

## 🆘 Troubleshooting

### Common Issues
1. **CORS errors**: Check allowed origins
2. **Email delivery**: Verify SendGrid configuration
3. **Environment variables**: Check all required vars are set
4. **API connection**: Verify backend URL in frontend

### Support Resources
- SendGrid documentation
- Vercel documentation
- Platform-specific guides
- GitHub repository issues

---

**Ready for production deployment! 🚀**
