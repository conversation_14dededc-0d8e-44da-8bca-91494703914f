#!/bin/bash

# Development startup script for Oyu Intelligence Portfolio
echo "🚀 Starting Oyu Intelligence Development Environment"
echo "=================================================="

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: Please run this script from the project root directory"
    exit 1
fi

# Function to start Python email service
start_python_service() {
    echo "📧 Starting Python Email Service..."
    cd api
    
    # Check if virtual environment exists
    if [ ! -d "venv" ]; then
        echo "🔧 Creating Python virtual environment..."
        python -m venv venv
    fi
    
    # Activate virtual environment
    source venv/bin/activate 2>/dev/null || source venv/Scripts/activate 2>/dev/null
    
    # Install dependencies
    echo "📦 Installing Python dependencies..."
    pip install -r requirements.txt
    
    # Start the service
    echo "🌟 Python Email Service starting on http://localhost:5000"
    python email_service.py &
    PYTHON_PID=$!
    
    cd ..
    echo "✅ Python Email Service started (PID: $PYTHON_PID)"
}

# Function to start Next.js
start_nextjs() {
    echo "⚛️ Starting Next.js Development Server..."
    
    # Install Node.js dependencies
    echo "📦 Installing Node.js dependencies..."
    npm install
    
    # Start Next.js
    echo "🌟 Next.js starting on http://localhost:3000"
    npm run dev &
    NEXTJS_PID=$!
    
    echo "✅ Next.js started (PID: $NEXTJS_PID)"
}

# Start services
start_python_service
sleep 2
start_nextjs

echo ""
echo "🎉 Development environment is ready!"
echo "=================================================="
echo "📱 Website: http://localhost:3000"
echo "📧 Email API: http://localhost:5000"
echo "🧪 Test Page: http://localhost:3000/test-contact"
echo "=================================================="
echo ""
echo "Press Ctrl+C to stop all services"

# Wait for user interrupt
trap 'echo "🛑 Stopping services..."; kill $PYTHON_PID $NEXTJS_PID 2>/dev/null; exit' INT
wait
