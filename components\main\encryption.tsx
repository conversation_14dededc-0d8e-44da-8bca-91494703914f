"use client";

import Image from "next/image";

import { SectionTitle } from "@/components/ui/section-title";

export const Encryption = () => {
  return (
    <section
      id="services"
      className="flex flex-row relative items-center justify-center min-h-screen w-full h-full -z-20 px-4 sm:px-6 lg:px-8"
    >
      <div className="absolute w-auto h-auto top-0 z-[5]">
        <SectionTitle
          title="Our Services"
          highlightWord="Services"
          size="medium"
          alignment="center"
        />
      </div>

      <div className="flex flex-col items-center justify-center translate-y-[-30px] sm:translate-y-[-50px] absolute z-[20] w-auto h-auto">
        <div className="flex flex-col items-center group cursor-pointer w-auto h-auto">
          <Image
            src="/lock-top.png"
            alt="AI Automation Icon"
            width={40}
            height={40}
            className="translate-y-4 sm:translate-y-5 transition-all duration-200 group-hover:translate-y-8 sm:group-hover:translate-y-11 w-[35px] h-[35px] sm:w-[40px] sm:h-[40px] md:w-[50px] md:h-[50px]"
          />
          <Image
            src="/lock-main.png"
            alt="AI Automation"
            width={70}
            height={70}
            className="z-10 w-[55px] h-[55px] sm:w-[60px] sm:h-[60px] md:w-[70px] md:h-[70px]"
          />
        </div>

        <div className="Welcome-box px-[12px] sm:px-[15px] py-[3px] sm:py-[4px] z-[20] border my-[15px] sm:my-[20px] border-[#7042F88B] opacity-[0.9]">
          <h1 className="Welcome-text text-[10px] sm:text-[12px]">AI Automation</h1>
        </div>
      </div>

      <div className="absolute z-[20] bottom-[8px] sm:bottom-[10px] px-[4px] sm:px-[5px]">
        <div className="cursive text-[16px] sm:text-[18px] md:text-[20px] font-medium text-center text-gray-300">
          Streamline your business with intelligent AI solutions.
        </div>
      </div>

      <div className="w-full flex items-start justify-center absolute">
        <video
          loop
          muted
          autoPlay
          playsInline
          preload="false"
          className="w-full h-auto"
        >
          <source src="/videos/encryption-bg.webm" type="video/webm" />
        </video>
      </div>
    </section>
  );
};
