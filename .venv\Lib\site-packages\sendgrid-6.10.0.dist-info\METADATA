Metadata-Version: 2.1
Name: sendgrid
Version: 6.10.0
Summary: Twilio SendGrid library for Python
Home-page: https://github.com/sendgrid/sendgrid-python/
Author: <PERSON>, <PERSON><PERSON><PERSON>
Author-email: <EMAIL>
License: MIT
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.5
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Requires-Python: >=2.7, !=3.0.*, !=3.1.*, !=3.2.*, !=3.3.*, !=3.4.*
License-File: LICENSE
Requires-Dist: python-http-client (>=3.2.1)
Requires-Dist: starkbank-ecdsa (>=2.0.1)

.. image:: https://github.com/sendgrid/sendgrid-python/raw/HEAD/twilio_sendgrid_logo.png
   :target: https://www.sendgrid.com



|Tests Badge| |codecov| |Python Versions| |PyPI Version| |Docker Badge| |MIT licensed| |Twitter Follow| |GitHub contributors| |Open Source Helpers|

**This library allows you to quickly and easily use the Twilio SendGrid Web API v3 via Python.**

**NEW:**

-  Version 6.X release is a BREAKING CHANGE from version 5.X, please see the `release notes`_ for details.
-  Send SMS messages with `Twilio`_.

This library provides full support for all Twilio SendGrid `Web API v3`_ endpoints, including `v3 /mail/send`_.

We want this library to be community driven and Twilio SendGrid led.
We need your help to realize this goal.
To help make sure we are building the right things in the right order,
we ask that you create `issues`_ and `pull requests`_ or simply upvote or comment on existing issues or pull requests.

Please browse the rest of this README for further detail.

We appreciate your continued support, thank you!

Table of Contents
=================

-  `Installation <#installation>`__
-  `Quick Start <#quick-start>`__
-  `Common Use Cases <#use-cases>`__
-  `General Usage <#usage>`__
-  `Processing Inbound Email <#processing-inbound-email>`__
-  `Announcements <#announcements>`__
-  `How to Contribute <#how-to-contribute>`__
-  `Troubleshooting <#troubleshooting>`__
-  `About <#about>`__
-  `License <#license>`__

Installation
============

Prerequisites
-------------

-  Python version 2.7 and 3.5+
-  For email, you will need a Twilio SendGrid account, starting at the `free level`_
-  For SMS messages, you will need a free `Twilio account`_

Setup Environment Variables
---------------------------

Mac
~~~

Update the development environment with your `SENDGRID_API_KEY`_ (more info `here <https://sendgrid.com/docs/User_Guide/Settings/api_keys.html>`__), for example:

.. code:: bash

    echo "export SENDGRID_API_KEY='YOUR_API_KEY'" > sendgrid.env
    echo "sendgrid.env" >> .gitignore
    source ./sendgrid.env

Twilio SendGrid also supports local environment file ``.env``.
Copy or rename ``.env_sample`` into ``.env`` and update `SENDGRID_API_KEY`_ with your key.

Windows
~~~~~~~

Temporarily set the environment variable (accessible only during the current CLI session):

.. code:: bash

    set SENDGRID_API_KEY=YOUR_API_KEY

Permanently set the environment variable (accessible in all subsequent CLI sessions):

.. code:: bash

    setx SENDGRID_API_KEY "YOUR_API_KEY"

Install Package
---------------

.. code:: bash

    pip install sendgrid

Dependencies
------------

-  `Python-HTTP-Client`_
-  `ECDSA-Python`_

Quick Start
===========

Hello Email
-----------

The following is the minimum needed code to send an email with the `/mail/send Helper`_
(`here <https://github.com/sendgrid/sendgrid-python/blob/HEAD/use_cases/kitchen_sink.md>`__ is a full example):

With Mail Helper Class
~~~~~~~~~~~~~~~~~~~~~~

.. code:: python

    import os
    from sendgrid import SendGridAPIClient
    from sendgrid.helpers.mail import Mail

    message = Mail(
        from_email='<EMAIL>',
        to_emails='<EMAIL>',
        subject='Sending with Twilio SendGrid is Fun',
        html_content='<strong>and easy to do anywhere, even with Python</strong>')
    try:
        sg = SendGridAPIClient(os.environ.get('SENDGRID_API_KEY'))
        response = sg.send(message)
        print(response.status_code)
        print(response.body)
        print(response.headers)
    except Exception as e:
        print(str(e))

The ``Mail`` constructor creates a `personalization object`_ for you.
`Here <https://github.com/sendgrid/sendgrid-python/blob/HEAD/use_cases/kitchen_sink.md>`__ is an example of how to add it.

Without Mail Helper Class
~~~~~~~~~~~~~~~~~~~~~~~~~

The following is the minimum needed code to send an email without the /mail/send Helper
(`here <https://github.com/sendgrid/sendgrid-python/blob/HEAD/examples/mail/mail.py#L27>`__ is a full example):

.. code:: python

    import os
    from sendgrid import SendGridAPIClient

    message = {
        'personalizations': [
            {
                'to': [
                    {
                        'email': '<EMAIL>'
                    }
                ],
                'subject': 'Sending with Twilio SendGrid is Fun'
            }
        ],
        'from': {
            'email': '<EMAIL>'
        },
        'content': [
            {
                'type': 'text/plain',
                'value': 'and easy to do anywhere, even with Python'
            }
        ]
    }
    try:
        sg = SendGridAPIClient(os.environ.get('SENDGRID_API_KEY'))
        response = sg.send(message)
        print(response.status_code)
        print(response.body)
        print(response.headers)
    except Exception as e:
        print(str(e))

General v3 Web API Usage (With `Fluent Interface`_)
---------------------------------------------------

.. code:: python

    import os
    from sendgrid import SendGridAPIClient

    sg = SendGridAPIClient(os.environ.get('SENDGRID_API_KEY'))
    response = sg.client.suppression.bounces.get()
    print(response.status_code)
    print(response.body)
    print(response.headers)

General v3 Web API Usage (Without `Fluent Interface`_)
------------------------------------------------------

.. code:: python

    import os
    from sendgrid import SendGridAPIClient

    sg = SendGridAPIClient(os.environ.get('SENDGRID_API_KEY'))
    response = sg.client._('suppression/bounces').get()
    print(response.status_code)
    print(response.body)
    print(response.headers)

Processing Inbound Email
========================

Please see `our helper`_ for utilizing our Inbound Parse webhook.

Usage
=====

-  `Twilio SendGrid Documentation`_
-  `Library Usage Documentation`_
-  `Example Code`_
-  `How-to: Migration from v2 to v3`_
-  `v3 Web API Mail Send Helper`_ - build a request object payload for a v3 /mail/send API call.
-  `Processing Inbound Email`_

Use Cases
=========

`Examples of common API use cases`_, such as how to send an email with a transactional template or add an attachment or send an SMS message.

Announcements
=============

All updates to this library are documented in our `CHANGELOG`_ and `releases`_.

How to Contribute
=================

We encourage contribution to our libraries (you might even score some nifty swag), please see our `CONTRIBUTING`_ guide for details.

Quick links:

-  `Feature Request`_
-  `Bug Reports`_
-  `Improvements to the Codebase`_
-  `Review Pull Requests`_

Troubleshooting
===============

Please see our `troubleshooting guide`_ for common library issues.

About
=====

**sendgrid-python** is maintained and funded by Twilio SendGrid, Inc.
The names and logos for **sendgrid-python** are trademarks of Twilio SendGrid, Inc.

License
=======

`The MIT License (MIT)`_

.. _Twilio: https://github.com/sendgrid/sendgrid-python/blob/HEAD/use_cases/sms.md
.. _release notes: https://github.com/sendgrid/sendgrid-python/releases/tag/v6.0.0
.. _Web API v3: https://sendgrid.com/docs/API_Reference/Web_API_v3/index.html
.. _v3 /mail/send: https://sendgrid.com/blog/introducing-v3mailsend-sendgrids-new-mail-endpoint
.. _issues: https://github.com/sendgrid/sendgrid-python/issues
.. _pull requests: https://github.com/sendgrid/sendgrid-python/blob/HEAD/CONTRIBUTING.md
.. _free level: https://sendgrid.com/free?source=sendgrid-python
.. _Twilio account: https://www.twilio.com/try-twilio?source=sendgrid-python
.. _SENDGRID_API_KEY: https://app.sendgrid.com/settings/api_keys
.. _Python-HTTP-Client: https://github.com/sendgrid/python-http-client
.. _ECDSA-Python: https://github.com/starkbank/ecdsa-python
.. _/mail/send Helper: https://github.com/sendgrid/sendgrid-python/tree/HEAD/sendgrid/helpers/mail
.. _personalization object: https://sendgrid.com/docs/Classroom/Send/v3_Mail_Send/personalizations.html
.. _Fluent Interface: https://sendgrid.com/blog/using-python-to-implement-a-fluent-interface-to-any-rest-api/
.. _our helper: https://github.com/sendgrid/sendgrid-python/tree/HEAD/sendgrid/helpers/inbound
.. _Twilio SendGrid Documentation: https://sendgrid.com/docs/API_Reference/index.html
.. _Library Usage Documentation: https://github.com/sendgrid/sendgrid-python/tree/HEAD/USAGE.md
.. _Example Code: https://github.com/sendgrid/sendgrid-python/tree/HEAD/examples
.. _`How-to: Migration from v2 to v3`: https://sendgrid.com/docs/Classroom/Send/v3_Mail_Send/how_to_migrate_from_v2_to_v3_mail_send.html
.. _v3 Web API Mail Send Helper: https://github.com/sendgrid/sendgrid-python/tree/HEAD/sendgrid/helpers/mail
.. _Processing Inbound Email: https://github.com/sendgrid/sendgrid-python/tree/HEAD/sendgrid/helpers/inbound
.. _Examples of common API use cases: https://github.com/sendgrid/sendgrid-python/blob/HEAD/use_cases/README.md
.. _breaking changes: https://github.com/sendgrid/sendgrid-python/issues/217
.. _CHANGELOG: https://github.com/sendgrid/sendgrid-python/blob/HEAD/CHANGELOG.md
.. _releases: https://github.com/sendgrid/sendgrid-python/releases
.. _CONTRIBUTING: https://github.com/sendgrid/sendgrid-python/blob/HEAD/CONTRIBUTING.md
.. _Feature Request: https://github.com/sendgrid/sendgrid-python/blob/HEAD/CONTRIBUTING.md#feature-request
.. _Bug Reports: https://github.com/sendgrid/sendgrid-python/blob/HEAD/CONTRIBUTING.md#submit-a-bug-report
.. _Improvements to the Codebase: https://github.com/sendgrid/sendgrid-python/blob/HEAD/CONTRIBUTING.md#improvements-to-the-codebase
.. _Review Pull Requests: https://github.com/sendgrid/sendgrid-python/blob/HEAD/CONTRIBUTING.md#code-reviews
.. _troubleshooting guide: https://github.com/sendgrid/sendgrid-python/blob/HEAD/TROUBLESHOOTING.md
.. _The MIT License (MIT): https://github.com/sendgrid/sendgrid-python/blob/HEAD/LICENSE

.. |Tests Badge| image:: https://github.com/sendgrid/sendgrid-python/actions/workflows/test.yml/badge.svg
   :target: https://github.com/sendgrid/sendgrid-python/actions/workflows/test.yml
.. |Python Versions| image:: https://img.shields.io/pypi/pyversions/sendgrid.svg
   :target: https://pypi.org/project/sendgrid/
.. |PyPI Version| image:: https://img.shields.io/pypi/v/sendgrid.svg
   :target: https://pypi.org/project/sendgrid/
.. |codecov| image:: https://img.shields.io/codecov/c/github/sendgrid/sendgrid-python/main.svg?style=flat-square&label=Codecov+Coverage
   :target: https://codecov.io/gh/sendgrid/sendgrid-python
.. |Docker Badge| image:: https://img.shields.io/docker/automated/sendgrid/sendgrid-python.svg
   :target: https://hub.docker.com/r/sendgrid/sendgrid-python/
.. |MIT licensed| image:: https://img.shields.io/badge/license-MIT-blue.svg
   :target: ./LICENSE
.. |Twitter Follow| image:: https://img.shields.io/twitter/follow/sendgrid.svg?style=social&label=Follow
   :target: https://twitter.com/sendgrid
.. |GitHub contributors| image:: https://img.shields.io/github/contributors/sendgrid/sendgrid-python.svg
   :target: https://github.com/sendgrid/sendgrid-python/graphs/contributors
.. |Open Source Helpers| image:: https://www.codetriage.com/sendgrid/sendgrid-python/badges/users.svg
   :target: https://www.codetriage.com/sendgrid/sendgrid-python
