# 🔧 Contact Form Troubleshooting Guide

## 🚨 Issue: Contact form not sending emails

If the contact form appears to submit but no emails are received, follow these steps:

## 📋 Step-by-Step Debugging

### Step 1: Check if Services are Running

**Check Python Email Service:**
```bash
curl http://localhost:5000/health
```
Expected response: `{"service":"email-service","status":"healthy","timestamp":"..."}`

**Check Next.js Development Server:**
- Visit: http://localhost:3000
- Should load the website

### Step 2: Test Services Individually

**Test Python Email Service Directly:**
```bash
curl -X POST http://localhost:5000/send-email \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test User",
    "email": "<EMAIL>",
    "subject": "Direct Test",
    "message": "Testing Python service directly"
  }'
```
Expected response: `{"success":true,"message":"Email sent successfully","status_code":202}`

**Test Next.js API Route:**
```bash
curl -X POST http://localhost:3000/api/contact \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test User",
    "email": "<EMAIL>",
    "subject": "API Test",
    "message": "Testing Next.js API route"
  }'
```
Expected response: `{"success":true,"message":"Email sent successfully","data":{...}}`

### Step 3: Use the Test Page

1. Visit: http://localhost:3000/test-contact
2. Fill out the form and submit
3. Check browser console (F12) for any errors
4. Use the "Test Python Service" and "Test Next.js API" buttons

### Step 4: Check Browser Console

1. Open your website: http://localhost:3000
2. Open browser developer tools (F12)
3. Go to Console tab
4. Fill out and submit the contact form
5. Look for these log messages:
   - 🚀 Form submission started
   - 📝 Form data: {...}
   - ✅ Validation passed, sending to API...
   - 📡 API Response status: 200
   - 🎉 Email sent successfully!

### Step 5: Check Terminal Logs

**Python Service Terminal:**
Look for these messages when form is submitted:
- INFO - Email sent successfully. Status code: 202

**Next.js Terminal:**
Look for these messages:
- 🔥 Next.js API: Contact form submission received
- 📝 Next.js API: Request body: {...}
- ✅ Next.js API: Validation passed
- 🔗 Next.js API: Calling Python API at: http://localhost:5000
- ✅ Next.js API: Python API success: {...}

## 🛠️ Common Issues & Solutions

### Issue 1: Python Service Not Running
**Symptoms:** `curl http://localhost:5000/health` fails
**Solution:**
```bash
cd api
python email_service.py
```

### Issue 2: Next.js Not Running
**Symptoms:** Website doesn't load at http://localhost:3000
**Solution:**
```bash
npm run dev
```

### Issue 3: Port Conflicts
**Symptoms:** "Port already in use" errors
**Solution:**
- Kill processes using ports 3000 and 5000
- Or change ports in configuration

### Issue 4: Form Validation Errors
**Symptoms:** Error messages in browser console
**Solution:**
- Ensure all fields are filled
- Check email format is valid
- Look for specific error messages

### Issue 5: API Connection Failed
**Symptoms:** "Failed to send email" error
**Solution:**
- Ensure Python service is running on port 5000
- Check if firewall is blocking connections
- Verify PYTHON_API_URL in .env.local

### Issue 6: SendGrid Errors
**Symptoms:** 403 Forbidden or other SendGrid errors
**Solution:**
- Verify sender email in SendGrid dashboard
- Check API key is correct
- Ensure TEST_MODE is set to False

## 🚀 Quick Fix Commands

**Restart Everything:**
```bash
# Kill any existing processes
# Then start fresh:

# Terminal 1:
cd api
python email_service.py

# Terminal 2:
npm run dev
```

**Or use the batch file (Windows):**
```bash
start-services.bat
```

## 📧 Email Delivery Notes

- **Delivery Time:** Emails may take 1-5 minutes to arrive
- **Check Spam:** Look in spam/junk folder
- **SendGrid Status:** Check SendGrid dashboard for delivery status
- **Email Address:** Emails are sent to `<EMAIL>`

## 🔍 Advanced Debugging

### Enable Detailed Logging

The contact form and API now include detailed console logging. Check:

1. **Browser Console** (F12 → Console)
2. **Next.js Terminal** (where you ran `npm run dev`)
3. **Python Terminal** (where you ran `python email_service.py`)

### Test with Different Data

Try submitting with:
- Different email addresses
- Different message lengths
- Special characters in messages

### Check Network Tab

In browser developer tools:
1. Go to Network tab
2. Submit the form
3. Look for the `/api/contact` request
4. Check if it returns 200 status
5. Examine the response

## 📞 Still Having Issues?

If the contact form still doesn't work after following these steps:

1. **Check all console logs** for error messages
2. **Test each service individually** using the curl commands
3. **Use the test page** at http://localhost:3000/test-contact
4. **Verify email delivery** by checking your inbox and spam folder
5. **Check SendGrid dashboard** for email delivery status

The debugging logs will help identify exactly where the issue is occurring.
