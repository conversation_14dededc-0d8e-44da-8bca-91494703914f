#!/usr/bin/env python3
"""
Simple test script to verify SendGrid configuration
Based on the official SendGrid Python guide
"""

import sendgrid
from sendgrid.helpers.mail import Mail
import os

# Try to load environment variables from .env.local
try:
    from dotenv import load_dotenv
    load_dotenv('../.env.local')
except ImportError:
    print("Note: python-dotenv not installed. Using system environment variables.")

# SendGrid configuration from environment
SENDGRID_API_KEY = os.getenv('SENDGRID_API_KEY')
FROM_EMAIL = os.getenv('FROM_EMAIL', '<EMAIL>')
TO_EMAIL = os.getenv('TO_EMAIL', '<EMAIL>')

def test_sendgrid():
    """Test SendGrid email sending"""
    print("Testing SendGrid email sending...")
    print(f"From: {FROM_EMAIL}")
    print(f"To: {TO_EMAIL}")
    print("-" * 50)
    
    try:
        # Create the email message
        message = Mail(
            from_email=FROM_EMAIL,
            to_emails=TO_EMAIL,
            subject='SendGrid Test Email',
            html_content='<strong>This is a test email from Oyu Intelligence contact form setup.</strong>'
        )
        
        # Send the email
        sg = sendgrid.SendGridAPIClient(api_key=SENDGRID_API_KEY)
        response = sg.send(message)
        
        print(f"✅ Email sent successfully!")
        print(f"Status Code: {response.status_code}")
        print(f"Response Body: {response.body}")
        print(f"Response Headers: {response.headers}")
        
        if response.status_code == 202:
            print("🎉 SendGrid accepted the email for delivery!")
        else:
            print(f"⚠️ Unexpected status code: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error sending email: {str(e)}")
        if hasattr(e, 'status_code'):
            print(f"Status Code: {e.status_code}")
        if hasattr(e, 'body'):
            print(f"Error Body: {e.body}")
        return False
    
    return True

if __name__ == "__main__":
    print("🧪 SendGrid Test Script")
    print("=" * 50)
    
    # Test the configuration
    success = test_sendgrid()
    
    if success:
        print("\n✅ SendGrid test completed successfully!")
        print("You should receive a test email shortly.")
    else:
        print("\n❌ SendGrid test failed!")
        print("Please check your API key and sender email verification.")
    
    print("\nNext steps:")
    print("1. Check your email inbox for the test message")
    print("2. If you received it, the email service should work")
    print("3. If not, verify your sender email in SendGrid dashboard")
