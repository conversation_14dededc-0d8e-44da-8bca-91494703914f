"use client";

import { motion, useInView } from "framer-motion";
import Image from "next/image";
import Link from "next/link";
import { useRef } from "react";

import { SectionTitle } from "@/components/ui/section-title";
import { BlogPostType } from "@/types";
import { GlowingEffect } from "@/components/ui/glowing-effect";
import Orb from "@/components/ui/Orb";

// Blog card component
const BlogCard = ({
  post,
  index
}: {
  post: BlogPostType;
  index: number;
}) => {
  const cardRef = useRef(null);
  const isInView = useInView(cardRef, { once: true });

  return (
    <motion.div
      ref={cardRef}
      initial={{ opacity: 0, y: 50 }}
      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      className="group h-full"
    >
      <Link href={`/blog/${post.slug}`} className="block h-full">
        <motion.div
          className="relative h-full flex flex-col overflow-hidden rounded-2xl backdrop-blur-xl bg-gradient-to-br from-[rgba(15,5,30,0.8)] via-[rgba(10,3,25,0.9)] to-[rgba(3,0,20,0.95)] border border-purple-500/20 group-hover:border-purple-400/50 transition-all duration-500 shadow-xl shadow-purple-900/20"
          whileHover={{
            y: -12,
            scale: 1.02,
            transition: { duration: 0.3, ease: "easeOut" }
          }}
        >
          {/* Enhanced glowing effect */}
          <GlowingEffect
            spread={120}
            glow={true}
            disabled={false}
            proximity={180}
            inactiveZone={0.03}
            blur={6}
            borderWidth={2}
          />

          {/* Enhanced image section */}
          <div className="relative h-56 w-full overflow-hidden rounded-t-2xl">
            {/* Background gradient overlay */}
            <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-transparent to-cyan-900/20 z-10" />

            <motion.div
              className="h-full w-full relative"
              whileHover={{ scale: 1.1 }}
              transition={{ duration: 0.7, ease: "easeOut" }}
            >
              <Image
                src={post.image}
                alt={post.title}
                fill
                className="object-cover transition-all duration-700 group-hover:brightness-110"
              />
              {/* Dynamic overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-60 group-hover:opacity-30 transition-opacity duration-500 z-20" />
            </motion.div>

            {/* Enhanced category badge */}
            <motion.div
              className="absolute top-4 right-4 z-30"
              whileHover={{ scale: 1.1, rotate: 5 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-purple-600 to-cyan-600 rounded-full blur-sm opacity-75" />
                <div className="relative bg-gradient-to-r from-purple-600 to-cyan-600 text-white text-xs font-semibold px-3 py-1.5 rounded-full shadow-lg backdrop-blur-sm">
                  {post.category}
                </div>
              </div>
            </motion.div>

            {/* Reading time indicator */}
            <div className="absolute bottom-4 left-4 z-30">
              <div className="bg-black/50 backdrop-blur-sm text-white text-xs px-2 py-1 rounded-full border border-white/20">
                {post.readTime}
              </div>
            </div>

            {/* Floating particles on hover */}
            <div className="absolute inset-0 pointer-events-none z-20 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
              {[...Array(4)].map((_, i) => (
                <motion.div
                  key={`blog-particle-${post.id}-${i}`}
                  className="absolute w-1 h-1 bg-gradient-to-r from-purple-400 to-cyan-400 rounded-full"
                  style={{
                    left: `${20 + i * 20}%`,
                    top: `${30 + i * 15}%`,
                  }}
                  animate={{
                    y: [-5, -15, -5],
                    opacity: [0.3, 0.8, 0.3],
                    scale: [0.5, 1, 0.5],
                  }}
                  transition={{
                    duration: 2 + i * 0.5,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: i * 0.2,
                  }}
                />
              ))}
            </div>
          </div>

          {/* Enhanced content section */}
          <div className="relative p-6 flex flex-col flex-grow">
            {/* Subtle background pattern */}
            <div className="absolute inset-0 opacity-5">
              <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-purple-500/10 to-cyan-500/10" />
            </div>

            <div className="relative z-10 flex flex-col h-full">
              {/* Enhanced author section */}
              <div className="flex items-center mb-4">
                <motion.div
                  className="relative w-12 h-12 rounded-full overflow-hidden mr-3 border-2 border-purple-500/30 group-hover:border-purple-400/50 transition-colors duration-300"
                  whileHover={{ scale: 1.1 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-cyan-500/20" />
                  <Image
                    src={post.authorImage}
                    alt={post.author}
                    fill
                    className="object-cover"
                  />
                </motion.div>
                <div className="text-sm">
                  <span className="text-white font-semibold group-hover:text-purple-200 transition-colors duration-300">
                    {post.author}
                  </span>
                  <div className="flex items-center text-gray-400 group-hover:text-gray-300 transition-colors duration-300">
                    <span>{post.date}</span>
                    <span className="mx-2">•</span>
                    <span className="text-purple-400">{post.readTime}</span>
                  </div>
                </div>
              </div>

              {/* Enhanced title */}
              <motion.h3
                className="text-xl font-bold text-white mb-3 group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r group-hover:from-purple-400 group-hover:to-cyan-400 transition-all duration-500 line-clamp-2 leading-tight"
                whileHover={{ scale: 1.02 }}
              >
                {post.title}
              </motion.h3>

              {/* Enhanced excerpt */}
              <p className="text-gray-300 group-hover:text-gray-200 text-sm mb-6 flex-grow line-clamp-3 leading-relaxed transition-colors duration-300">
                {post.excerpt}
              </p>

              {/* Enhanced CTA */}
              <div className="mt-auto">
                <motion.div
                  className="flex items-center text-purple-400 group-hover:text-purple-300 transition-colors duration-300"
                  whileHover={{ x: 5 }}
                  transition={{ type: "spring", stiffness: 400 }}
                >
                  <span className="text-sm font-semibold">Read article</span>
                  <motion.svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4 ml-2"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    animate={{ x: [0, 3, 0] }}
                    transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                  </motion.svg>
                </motion.div>
              </div>
            </div>
          </div>

          {/* Enhanced shadow */}
          <div className="absolute inset-0 rounded-2xl shadow-2xl shadow-purple-900/20 group-hover:shadow-purple-500/30 transition-shadow duration-500 pointer-events-none" />
        </motion.div>
      </Link>
    </motion.div>
  );
};

export const BlogPreview = () => {
  const sectionRef = useRef(null);
  const isInView = useInView(sectionRef, { once: true, amount: 0.2 });

  const blogPosts: BlogPostType[] = [
    {
      id: 1,
      slug: "ai-automation-trends-2024",
      title: "Top AI Automation Trends to Watch in 2024",
      excerpt: "Discover the emerging AI automation trends that are transforming businesses and creating new opportunities for growth and innovation.",
      content: "",
      image: "/blog/ai-trends.jpg",
      author: "Boldbat Khuukhenduu",
      authorImage: "/testimonials/person1.jpg",
      date: "Jan 15, 2024",
      category: "AI Automation",
      readTime: "5 min read"
    },
    {
      id: 2,
      slug: "mobile-app-development-best-practices",
      title: "Best Practices for Successful Mobile App Development",
      excerpt: "Learn the essential best practices that can help ensure your mobile app development project succeeds in today's competitive market.",
      content: "",
      image: "/blog/mobile-app.jpg",
      author: "Boldbat Khuukhenduu",
      authorImage: "/testimonials/person2.jpg",
      date: "Feb 3, 2024",
      category: "Mobile App",
      readTime: "7 min read"
    },
    {
      id: 3,
      slug: "web-design-trends-2024",
      title: "Web Design Trends That Will Dominate in 2024",
      excerpt: "Stay ahead of the curve with these cutting-edge web design trends that are set to define the digital landscape in 2024.",
      content: "",
      image: "/blog/web-design.jpg",
      author: "Boldbat Khuukhenduu",
      authorImage: "/testimonials/person3.jpg",
      date: "Feb 18, 2024",
      category: "Web Development",
      readTime: "6 min read"
    },
  ];

  return (
    <section
      id="blog"
      ref={sectionRef}
      className="relative flex flex-col items-center justify-center py-20 px-4 overflow-hidden"
    >
      {/* Background elements */}
      <div className="absolute top-1/4 left-0 w-72 h-72 bg-purple-900/20 rounded-full blur-3xl"></div>
      <div className="absolute bottom-1/4 right-0 w-72 h-72 bg-cyan-900/20 rounded-full blur-3xl"></div>

      <SectionTitle
        subtitle="Latest Insights"
        title="Our Blog"
        highlightWord="Blog"
        description="Stay updated with the latest trends, insights, and best practices in technology and digital innovation."
        size="large"
        alignment="center"
        icon={
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
          </svg>
        }
      />

      {/* Blog posts grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 max-w-6xl px-4 sm:px-6 lg:px-8">
        {blogPosts.map((post, index) => (
          <BlogCard
            key={post.id}
            post={post}
            index={index}
          />
        ))}
      </div>

      {/* View all button */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
        transition={{ duration: 0.5, delay: 0.6 }}
        className="mt-16"
      >
        <Link
          href="/blog"
          className="px-8 py-4 bg-gradient-to-r from-purple-600 to-cyan-600 text-white rounded-lg hover:opacity-90 transition-all duration-300 flex items-center font-medium shadow-lg shadow-purple-900/20 hover:shadow-purple-800/30 hover:-translate-y-1"
        >
          <span>View All Articles</span>
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2 group-hover:translate-x-1 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
          </svg>
        </Link>
      </motion.div>

      {/* Orb Component */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
        transition={{ duration: 0.5, delay: 0.8 }}
        className="mt-32 mb-32"
        style={{ width: '100%', height: '800px', position: 'relative' }}
      >
        <Orb
          hoverIntensity={0.5}
          rotateOnHover={true}
          hue={0}
          forceHoverState={false}
        />
      </motion.div>
    </section>
  );
};
